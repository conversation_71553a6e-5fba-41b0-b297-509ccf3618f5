<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            direction: rtl;
            text-align: right;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #4472C4;
            padding-bottom: 15px;
        }

        .header .logo {
            max-width: 120px;
            height: auto;
            margin: 0 auto 15px;
            display: block;
        }

        .header h1 {
            font-size: 18px;
            font-weight: bold;
            color: #4472C4;
            margin-bottom: 10px;
        }

        .header .info {
            font-size: 11px;
            color: #666;
        }

        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding: 5px;
            background-color: #fff;
        }

        .footer img {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 5px;
            vertical-align: middle;
        }
        
        .group-section {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }
        
        .group-title {
            background-color: #4472C4;
            color: white;
            padding: 8px 12px;
            font-weight: bold;
            font-size: 13px;
            margin-bottom: 10px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            font-size: 10px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 6px 8px;
            text-align: center;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .group-total {
            background-color: #e9ecef;
            font-weight: bold;
        }
        
        .final-summary {
            margin-top: 20px;
            border-top: 2px solid #4472C4;
            padding-top: 15px;
        }
        
        .summary-title {
            font-size: 14px;
            font-weight: bold;
            color: #4472C4;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .summary-table {
            background-color: #f8f9fa;
        }
        
        .summary-table th {
            background-color: #4472C4;
            color: white;
        }
        
        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
        
        @page {
            size: A4;
            margin: 1cm;
        }
    </style>
</head>
<body>
    <!-- رأس التقرير -->
    <div class="header">
        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="logo">
        <h1>{{ title }}</h1>
        <div class="info">
            <div>الفترة الزمنية: {{ date_range_text }}</div>
            <div>تاريخ التقرير: {{ current_date }}</div>
        </div>
    </div>

    <!-- بيانات المجموعات -->
    {% if groups_data %}
        {% for group_info in groups_data %}
        <div class="group-section">
            <div class="group-title">
                {{ group_info.category }} - {{ group_info.type }}
                ({{ group_info.data|length }} عنصر)
            </div>
            
            {% if group_info.data %}
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الصنف</th>
                        <th>النوع</th>
                        <th>الفئة</th>
                        <th>الوحدة</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <th>المعدل</th>
                        <th>الرصيد</th>
                        <th>عدد الحالات</th>
                        <th>التكلفة</th>
                        {% if show_location %}
                        <th>{{ location_type }}</th>
                        {% endif %}
                    </tr>
                </thead>
                <tbody>
                    {% for item in group_info.data %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ item.name }}</td>
                        <td>{{ item.type }}</td>
                        <td>{{ item.category }}</td>
                        <td>{{ item.unit }}</td>
                        <td>{{ "%.2f"|format(item.price) }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>{{ item.rate or '-' }}</td>
                        <td>{{ item.balance }}</td>
                        <td>{{ item.cases_count }}</td>
                        <td>{{ "%.2f"|format(item.cost) }}</td>
                        {% if show_location %}
                        <td>
                            {% if location_type == 'العيادة' %}
                                {{ item.clinic_name }}
                            {% elif location_type == 'المنطقة' %}
                                {{ item.area_name }}
                            {% elif location_type == 'الفرع' %}
                                {{ item.branch_name }}
                            {% endif %}
                        </td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="group-total">
                        <th colspan="{% if show_location %}9{% else %}8{% endif %}">إجمالي المجموعة:</th>
                        <th>-</th>
                        <th>{{ group_info.total_cases }}</th>
                        <th>{{ "%.2f"|format(group_info.total_cost) }}</th>
                        {% if show_location %}
                        <th>-</th>
                        {% endif %}
                    </tr>
                </tfoot>
            </table>
            {% else %}
            <div class="no-data">لا توجد بيانات متاحة لهذه المجموعة</div>
            {% endif %}
        </div>
        {% endfor %}

        <!-- الملخص النهائي -->
        <div class="final-summary">
            <div class="summary-title">الملخص النهائي لجميع المجموعات</div>
            <table class="summary-table">
                <thead>
                    <tr>
                        <th>الفئة</th>
                        <th>النوع</th>
                        <th>عدد العناصر</th>
                        <th>إجمالي الحالات</th>
                        <th>إجمالي التكلفة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for group_info in groups_data %}
                    <tr>
                        <td>{{ group_info.category }}</td>
                        <td>{{ group_info.type }}</td>
                        <td>{{ group_info.data|length }}</td>
                        <td>{{ group_info.total_cases }}</td>
                        <td>{{ "%.2f"|format(group_info.total_cost) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="group-total">
                        <th colspan="3">الإجمالي العام:</th>
                        <th>{{ total_cases_all }}</th>
                        <th>{{ "%.2f"|format(total_cost_all) }}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    {% else %}
        <div class="no-data">
            لا توجد بيانات أنسولين متاحة للعرض في الفترة المحددة
        </div>
    {% endif %}

    <!-- حقوق الحفظ -->
    <div class="footer">
        <img src="{{ url_for('static', filename='images/Ahmed.jpeg') }}" alt="ك/أحمد علي أحمد">
        جميع الحقوق محفوظة لـ ك/أحمد علي أحمد (أحمد كوكب)
    </div>
</body>
</html>
