// ملف JavaScript مخصص لتطبيق منصرف الأدوية

// تنفيذ الكود عند اكتمال تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // إخفاء رسائل التنبيه تلقائياً بعد 5 ثوانٍ
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // مستمعي الأحداث للقوائم المنسدلة
    const branchSelect = document.getElementById('branch');
    const areaSelect = document.getElementById('area');
    const clinicSelect = document.getElementById('clinic');

    if (branchSelect) {
        branchSelect.addEventListener('change', (e) => {
            if (e.target.value) {
                updateAreas(e.target.value);
            }
        });
    }

    if (areaSelect) {
        areaSelect.addEventListener('change', (e) => {
            if (e.target.value) {
                updateClinics(e.target.value);
            }
        });
    }

    if (clinicSelect) {
        clinicSelect.addEventListener('change', (e) => {
            if (e.target.value) {
                const selectedOption = e.target.options[e.target.selectedIndex];
                handleClinicSelection(e.target.value, selectedOption.text);
            }
        });
    }

    // إذا كنا في صفحة الصرف، قم بتحديد العيادة تلقائياً
    if (window.location.pathname === '/dispense/new') {
        const selectedClinicId = localStorage.getItem('selectedClinicId');
        const selectedClinicName = localStorage.getItem('selectedClinicName');
        const clinicSelect = document.getElementById('clinic_id');
        
        if (selectedClinicId && selectedClinicName && clinicSelect) {
            // إضافة العيادة المحددة إلى القائمة إذا لم تكن موجودة
            if (!Array.from(clinicSelect.options).some(opt => opt.value === selectedClinicId)) {
                const option = new Option(selectedClinicName, selectedClinicId);
                clinicSelect.add(option);
            }
            
            // تحديد العيادة
            clinicSelect.value = selectedClinicId;
            
            // تحديث الحقل المخفي أيضاً
            const hiddenClinicId = document.getElementById('selected_clinic_id');
            if (hiddenClinicId) {
                hiddenClinicId.value = selectedClinicId;
            }
            
            // إطلاق حدث التغيير لتحديث أي عناصر مرتبطة
            clinicSelect.dispatchEvent(new Event('change'));
            
            // مسح البيانات المخزنة
            localStorage.removeItem('selectedClinicId');
            localStorage.removeItem('selectedClinicName');
        }

        // التحقق من وجود معرف العيادة في URL
        const urlParams = new URLSearchParams(window.location.search);
        const clinicId = urlParams.get('clinic_id');
        if (clinicId) {
            loadAndSelectClinic(clinicId);
        }
    }

    // عند تحميل صفحة الصرف، تحقق من وجود معرف العيادة المحدد
    if (window.location.pathname === '/dispense_new') {
        const selectedClinicId = localStorage.getItem('selectedClinicId');
        if (selectedClinicId) {
            // يمكنك استخدام معرف العيادة هنا لتحديث الواجهة
            document.getElementById('selected_clinic_id').value = selectedClinicId;
            // مسح المعرف بعد استخدامه
            localStorage.removeItem('selectedClinicId');
        }
    }
});

// دالة لحساب التكلفة الإجمالية
function calculateTotalCost(quantity, price) {
    return parseFloat(quantity) * parseFloat(price);
}

// دالة لتحديث حقل التكلفة الإجمالية
function updateTotalCost() {
    var quantity = document.getElementById('quantity');
    var price = document.getElementById('price');
    var totalCost = document.getElementById('total_cost');
    
    if (quantity && price && totalCost) {
        var cost = calculateTotalCost(quantity.value, price.value);
        totalCost.value = cost.toFixed(2);
    }
}

// إضافة مستمعي الأحداث لحقول الكمية والسعر
document.addEventListener('DOMContentLoaded', function() {
    var quantity = document.getElementById('quantity');
    var price = document.getElementById('price');
    
    if (quantity && price) {
        quantity.addEventListener('input', updateTotalCost);
        price.addEventListener('input', updateTotalCost);
    }
});

// دوال التحديث التلقائي للقوائم المنسدلة
function updateAreas(branchId) {
    fetch(`/get_areas/${branchId}`)
        .then(response => response.json())
        .then(data => {
            const areaSelect = document.getElementById('area');
            if (areaSelect) {
                areaSelect.innerHTML = '<option value="">اختر المنطقة</option>';
                data.forEach(area => {
                    areaSelect.innerHTML += `<option value="${area.id}">${area.name}</option>`;
                });
            }
            // إعادة تعيين العيادات عند تغيير الفرع
            const clinicSelect = document.getElementById('clinic_id');
            if (clinicSelect) {
                clinicSelect.innerHTML = '<option value="">اختر العيادة</option>';
            }
        });
}

function updateClinics(areaId) {
    fetch(`/get_clinics/${areaId}`)
        .then(response => response.json())
        .then(data => {
            const clinicSelect = document.getElementById('clinic_id');
            if (clinicSelect) {
                clinicSelect.innerHTML = '<option value="">اختر العيادة</option>';
                data.forEach(clinic => {
                    clinicSelect.innerHTML += `<option value="${clinic.id}">${clinic.name}</option>`;
                });
            }
        });
}

function handleClinicSelection(clinicId, clinicName) {
    // تخزين معرف العيادة واسمها في localStorage
    localStorage.setItem('selectedClinicId', clinicId);
    localStorage.setItem('selectedClinicName', clinicName);
    
    // إذا كان المستخدم في الصفحة الرئيسية، قم بتوجيهه إلى صفحة الصرف
    if (window.location.pathname === '/') {
        window.location.href = `/dispense/new?clinic_id=${clinicId}`;
    }
}

// دالة لتحميل معلومات العيادة وتحديث القائمة المنسدلة
function loadAndSelectClinic(clinicId) {
    fetch(`/get_clinic_info/${clinicId}`)
        .then(response => response.json())
        .then(clinic => {
            const clinicSelect = document.getElementById('clinic_id');
            if (clinicSelect) {
                // إضافة العيادة إلى القائمة إذا لم تكن موجودة
                if (!Array.from(clinicSelect.options).some(opt => opt.value === clinic.id.toString())) {
                    const option = new Option(clinic.name, clinic.id);
                    clinicSelect.add(option);
                }
                
                // تحديد العيادة
                clinicSelect.value = clinic.id;
                
                // تحديث الحقل المخفي
                const hiddenClinicId = document.getElementById('selected_clinic_id');
                if (hiddenClinicId) {
                    hiddenClinicId.value = clinic.id;
                }
                
                // إطلاق حدث التغيير
                clinicSelect.dispatchEvent(new Event('change'));
            }
        })
        .catch(error => console.error('Error loading clinic info:', error));
}
