<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            direction: rtl;
            text-align: right;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #4472C4;
            padding-bottom: 15px;
        }

        .header .logo {
            max-width: 120px;
            height: auto;
            margin: 0 auto 15px;
            display: block;
        }

        .header h1 {
            font-size: 18px;
            font-weight: bold;
            color: #4472C4;
            margin-bottom: 10px;
        }

        .header .info {
            font-size: 11px;
            color: #666;
            margin-bottom: 5px;
        }

        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding: 5px;
            background-color: #fff;
        }

        .footer img {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 5px;
            vertical-align: middle;
        }
        
        .filter-info {
            background-color: #e3f2fd;
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 11px;
            color: #1976d2;
            text-align: center;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            font-size: 10px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 6px 8px;
            text-align: center;
        }
        
        th {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
        }
        
        tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .total-row {
            background-color: #e9ecef;
            font-weight: bold;
        }
        
        .summary-section {
            margin-top: 20px;
            border-top: 2px solid #4472C4;
            padding-top: 15px;
        }
        
        .summary-title {
            font-size: 14px;
            font-weight: bold;
            color: #4472C4;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .summary-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 15px;
        }
        
        .stat-box {
            text-align: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        
        .stat-number {
            font-size: 16px;
            font-weight: bold;
            color: #4472C4;
        }
        
        .stat-label {
            font-size: 10px;
            color: #666;
        }
        
        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        
        @page {
            size: A4;
            margin: 1cm;
        }
    </style>
</head>
<body>
    <!-- رأس التقرير -->
    <div class="header">
        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="logo">
        <h1>{{ title }}</h1>
        <div class="info">الفترة الزمنية: {{ date_range_text }}</div>
        <div class="info">تاريخ التقرير: {{ current_date }}</div>
    </div>

    <!-- معلومات التصفية -->
    <div class="filter-info">
        نوع التصفية المطبقة: {{ filter_text }}
    </div>

    <!-- بيانات التقرير -->
    {% if insulin_items %}
        <!-- إحصائيات سريعة -->
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-number">{{ insulin_items|length }}</div>
                <div class="stat-label">عدد السجلات</div>
            </div>
            <div class="stat-box">
                <div class="stat-number">{{ total_cases }}</div>
                <div class="stat-label">إجمالي الحالات</div>
            </div>
            <div class="stat-box">
                <div class="stat-number">{{ "%.2f"|format(total_cost) }}</div>
                <div class="stat-label">إجمالي التكلفة</div>
            </div>
        </div>

        <!-- جدول البيانات -->
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>الصنف</th>
                    <th>النوع</th>
                    <th>الفئة</th>
                    <th>الوحدة</th>
                    <th>السعر</th>
                    <th>الكمية</th>
                    <th>المعدل</th>
                    <th>الرصيد</th>
                    <th>عدد الحالات</th>
                    <th>التكلفة</th>
                    {% if show_location %}
                    <th>{{ location_type }}</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for item in insulin_items %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.type }}</td>
                    <td>{{ item.category }}</td>
                    <td>{{ item.unit }}</td>
                    <td>{{ "%.2f"|format(item.price) }}</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ item.rate or '-' }}</td>
                    <td>{{ item.balance }}</td>
                    <td>{{ item.cases_count }}</td>
                    <td>{{ "%.2f"|format(item.cost) }}</td>
                    {% if show_location %}
                    <td>
                        {% if location_type == 'العيادة' %}
                            {{ item.clinic_name }}
                        {% elif location_type == 'المنطقة' %}
                            {{ item.area_name }}
                        {% elif location_type == 'الفرع' %}
                            {{ item.branch_name }}
                        {% endif %}
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr class="total-row">
                    <th colspan="{% if show_location %}9{% else %}8{% endif %}">الإجمالي:</th>
                    <th>-</th>
                    <th>{{ total_cases }}</th>
                    <th>{{ "%.2f"|format(total_cost) }}</th>
                    {% if show_location %}
                    <th>-</th>
                    {% endif %}
                </tr>
            </tfoot>
        </table>

        <!-- ملخص إضافي -->
        <div class="summary-section">
            <div class="summary-title">ملخص التقرير</div>
            <table>
                <thead>
                    <tr>
                        <th>البيان</th>
                        <th>القيمة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>إجمالي عدد السجلات</td>
                        <td>{{ insulin_items|length }}</td>
                    </tr>
                    <tr>
                        <td>إجمالي عدد الحالات</td>
                        <td>{{ total_cases }}</td>
                    </tr>
                    <tr>
                        <td>إجمالي التكلفة</td>
                        <td>{{ "%.2f"|format(total_cost) }} ريال</td>
                    </tr>
                    <tr>
                        <td>متوسط التكلفة لكل حالة</td>
                        <td>{{ "%.2f"|format(total_cost / total_cases if total_cases > 0 else 0) }} ريال</td>
                    </tr>
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="no-data">
            لا توجد بيانات أنسولين متاحة للعرض في الفترة والتصفية المحددة
        </div>
    {% endif %}

    <!-- حقوق الحفظ -->
    <div class="footer">
        <img src="{{ url_for('static', filename='images/Ahmed.jpeg') }}" alt="ك/أحمد علي أحمد">
        جميع الحقوق محفوظة لـ ك/أحمد علي أحمد (أحمد كوكب)
    </div>
</body>
</html>
