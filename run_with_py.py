#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل بديل يستخدم py launcher
"""

import sys
import os
import subprocess
from datetime import datetime

# إصلاح مشكلة ترميز النصوص العربية والرموز التعبيرية في Windows
if os.name == 'nt':  # Windows
    try:
        import ctypes
        ctypes.windll.kernel32.SetConsoleOutputCP(65001)
        ctypes.windll.kernel32.SetConsoleCP(65001)
    except Exception:
        pass

    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except AttributeError:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer,
                                      encoding='utf-8',
                                      errors='replace',
                                      line_buffering=True)
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer,
                                      encoding='utf-8',
                                      errors='replace',
                                      line_buffering=True)

def safe_print(text):
    """طباعة آمنة للنصوص العربية والرموز التعبيرية"""
    try:
        print(text)
    except UnicodeEncodeError:
        try:
            print(text.encode('utf-8', 'replace').decode('utf-8', 'replace'))
        except:
            print("رسالة نصية (تعذر عرض الترميز الأصلي)")
    except Exception as e:
        print(f"خطأ في الطباعة: {str(e)}")

def install_requirements():
    """تثبيت المكتبات المطلوبة"""
    safe_print("🔄 تثبيت المكتبات المطلوبة...")
    
    packages = ['flask', 'openpyxl']
    
    for package in packages:
        try:
            safe_print(f"  - تثبيت {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package], 
                                stdout=subprocess.DEVNULL, 
                                stderr=subprocess.DEVNULL)
            safe_print(f"  ✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            safe_print(f"  ⚠️ فشل تثبيت {package}")
        except Exception as e:
            safe_print(f"  ❌ خطأ في تثبيت {package}: {e}")

def main():
    safe_print("=" * 60)
    safe_print("🏥 نظام إدارة منصرف الأدوية والأنسولين")
    safe_print("=" * 60)
    safe_print("📋 المطور: أحمد علي أحمد (أحمد كوكب)")
    safe_print("📧 البريد الإلكتروني: <EMAIL>")
    safe_print("📱 الهاتف/واتساب: 01000314398")
    safe_print("🌐 الموقع الإلكتروني: <EMAIL>")
    safe_print("=" * 60)
    
    # تثبيت المكتبات
    install_requirements()
    
    try:
        safe_print("🔄 تحميل التطبيق...")
        from app import app

        # إضافة متغير التاريخ
        @app.context_processor
        def inject_now_date():
            return {'now_date': datetime.now().date()}

        safe_print("✅ تم تحميل التطبيق بنجاح")
        safe_print("🚀 بدء تشغيل الخادم على المنفذ 8080...")
        safe_print("🔗 رابط التطبيق: http://localhost:8080")
        safe_print("=" * 60)
        safe_print("📝 ملاحظة: اضغط Ctrl+C لإيقاف الخادم")
        safe_print("=" * 60)

        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=8080,
            debug=True,
            use_reloader=False
        )
        
    except ImportError as e:
        safe_print(f"❌ خطأ في تحميل التطبيق: {e}")
        safe_print("🔧 تأكد من تثبيت Flask: pip install flask")
        sys.exit(1)
        
    except KeyboardInterrupt:
        safe_print("\n" + "=" * 60)
        safe_print("⏹️  تم إيقاف الخادم بواسطة المستخدم")
        safe_print("=" * 60)
        
    except Exception as e:
        safe_print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        safe_print("=" * 60)
        sys.exit(1)

if __name__ == '__main__':
    main()
