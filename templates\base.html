<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}تطبيق منصرف الأدوية{% endblock %}</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Material Design Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css">
    <!-- أيقونة PDF المخصصة -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/pdf-icon.css') }}">
    <!-- Date Utils Script -->
    <script src="{{ url_for('static', filename='js/date-utils.js') }}"></script>
    <!-- Ensure Material Design Icons are loaded properly -->
    <style>
        .mdi::before {
            font-size: 24px;
            line-height: 1;
            display: inline-block;
            width: 1em;
            height: 1em;
        }
    </style>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #4e73df;
            --secondary-color: #858796;
            --success-color: #1cc88a;
            --info-color: #36b9cc;
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
            --light-color: #f8f9fc;
            --dark-color: #5a5c69;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fc;
            color: #5a5c69;
            transition: all 0.3s ease;
        }

        /* Navbar Styles */
        .navbar {
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            padding: 0.75rem 1rem;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            letter-spacing: 0.05em;
        }

        .dropdown-item:active {
            background-color: var(--primary-color);
        }

        .nav-link {
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            border-radius: 0.5rem;
            margin: 0 0.2rem;
        }

        .nav-link:hover {
            transform: translateY(-2px);
            background-color: rgba(255, 255, 255, 0.1);
        }

        .navbar-nav .nav-link.active {
            font-weight: 700;
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            border-radius: 0.5rem;
            padding: 0.5rem;
        }

        .dropdown-item {
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            border-radius: 0.3rem;
            transition: all 0.2s;
            margin-bottom: 0.2rem;
        }

        .dropdown-item:hover {
            background-color: rgba(78, 115, 223, 0.1);
        }

        .dropdown-item i {
            margin-left: 0.5rem;
            font-size: 1.1rem;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.15);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1.25rem 1.5rem;
            font-weight: 700;
        }

        .card-header:first-child {
            border-radius: 0.75rem 0.75rem 0 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Button Styles */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1.25rem;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #4262c5;
            border-color: #4262c5;
        }

        /* Form Styles */
        .form-control, .form-select {
            border-radius: 0.5rem;
            padding: 0.6rem 1rem;
            border: 1px solid #d1d3e2;
            transition: all 0.2s;
        }

        .form-control:focus, .form-select:focus {
            border-color: #bac8f3;
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
        }

        .input-group-text {
            border-radius: 0.5rem;
            background-color: #f8f9fc;
            border: 1px solid #d1d3e2;
        }

        /* Table Styles */
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .table th {
            font-weight: 700;
            background-color: #f8f9fc;
            border-bottom: 2px solid #e3e6f0;
        }

        /* Alert Styles */
        .alert {
            border-radius: 0.5rem;
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        /* Footer Styles */
        footer {
            box-shadow: 0 -0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
            padding: 2rem 0;
        }

        footer a {
            transition: all 0.2s;
        }

        footer a:hover {
            color: var(--primary-color) !important;
        }

        /* أنماط أزرار التقارير */
        .action-buttons {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .action-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            font-size: 24px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        .print-btn {
            background-color: #4e73df;
            color: white;
        }

        .print-btn:hover {
            background-color: #3a5fc9;
        }

        .back-btn {
            background-color: #6c757d;
            color: white;
        }

        .back-btn:hover {
            background-color: #5a6268;
        }

        .excel-btn {
            background-color: #1d6f42;
            color: white;
        }

        .excel-btn:hover {
            background-color: #185a36;
        }

        .pdf-btn {
            background-color: #e74a3b;
            color: white;
        }

        .pdf-btn:hover {
            background-color: #c0392b;
        }

        /* إخفاء العناصر عند الطباعة */
        @media print {
            .navbar, .sidebar, footer, .no-print, .action-buttons {
                display: none !important;
            }
            body {
                background-color: #fff !important;
                padding: 0 !important;
                margin: 0 !important;
            }
            .container, .container-fluid {
                padding: 0 !important;
                margin: 0 !important;
                width: 100% !important;
                max-width: 100% !important;
            }
            .card {
                border: none !important;
                box-shadow: none !important;
            }
            .card-header {
                background-color: #f8f9fc !important;
                color: #000 !important;
                border-bottom: 2px solid #4e73df !important;
            }
        }
    </style>

    {% block styles %}{% endblock %}
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" height="40" class="me-2">
                <span>منصرف الأدوية</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">
                            <i class="mdi mdi-home-variant me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="mdi mdi-hospital-building me-1"></i>إدارة العيادات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/manage/branches"><i class="mdi mdi-office-building"></i>الفروع</a></li>
                            <li><a class="dropdown-item" href="/manage/areas"><i class="mdi mdi-map"></i>المناطق</a></li>
                            <li><a class="dropdown-item" href="/manage/clinics"><i class="mdi mdi-hospital"></i>العيادات</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="mdi mdi-pill me-1"></i>إدارة الأدوية
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/manage/drug_categories"><i class="mdi mdi-tag-multiple"></i>التصنيفات</a></li>
                            <li><a class="dropdown-item" href="/manage/drugs"><i class="mdi mdi-pill"></i>الأدوية</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/manage/drug_group_codes"><i class="mdi mdi-code-tags"></i>تكويد المجموعات</a></li>
                            <li><a class="dropdown-item" href="/manage/drug_groups"><i class="mdi mdi-package-variant"></i>المجموعات الدوائية</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/manage/insulin"><i class="mdi mdi-needle"></i>إدارة الأنسولين</a></li>
                            <li><a class="dropdown-item" href="/insulin/dispense"><i class="mdi mdi-format-list-bulleted"></i>منصرف الأنسولين</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="mdi mdi-cart me-1"></i>صرف الأدوية
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/dispense"><i class="mdi mdi-pill"></i>صرف الأدوية</a></li>
                            <li><a class="dropdown-item" href="/insulin/dispense"><i class="mdi mdi-needle"></i>صرف الأنسولين</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports">
                            <i class="mdi mdi-chart-bar me-1"></i>التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/data-management">
                            <i class="mdi mdi-database-settings me-1"></i>إدارة البيانات
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="mdi mdi-information-outline me-1"></i>عن التطبيق
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('copyright') }}"><i class="mdi mdi-copyright"></i>حقوق الملكية ك/ أحمد علي أحمد (أحمد كوكب)</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('designer') }}"><i class="mdi mdi-account-badge"></i>نبذة عن المصمم</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show shadow-sm">
                        <div class="d-flex align-items-center">
                            {% if category == 'success' %}
                                <i class="mdi mdi-check-circle-outline me-2" style="font-size: 1.5rem;"></i>
                            {% elif category == 'danger' %}
                                <i class="mdi mdi-alert-circle-outline me-2" style="font-size: 1.5rem;"></i>
                            {% elif category == 'warning' %}
                                <i class="mdi mdi-alert-outline me-2" style="font-size: 1.5rem;"></i>
                            {% elif category == 'info' %}
                                <i class="mdi mdi-information-outline me-2" style="font-size: 1.5rem;"></i>
                            {% else %}
                                <i class="mdi mdi-bell-outline me-2" style="font-size: 1.5rem;"></i>
                            {% endif %}
                            <div>{{ message }}</div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-white text-center py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-3 mb-md-0">
                    <h5 class="text-primary mb-3">منصرف الأدوية</h5>
                    <p class="text-muted">نظام متكامل لإدارة صرف الأدوية للعيادات والإدارات وفقاً للمناطق داخل الفروع</p>
                </div>
                <div class="col-md-4 mb-3 mb-md-0">
                    <h5 class="text-primary mb-3">روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="/" class="text-decoration-none text-muted"><i class="mdi mdi-chevron-left me-1"></i>الرئيسية</a></li>
                        <li><a href="/dispense" class="text-decoration-none text-muted"><i class="mdi mdi-chevron-left me-1"></i>صرف الأدوية</a></li>
                        <li><a href="/reports" class="text-decoration-none text-muted"><i class="mdi mdi-chevron-left me-1"></i>التقارير</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5 class="text-primary mb-3">إدارة النظام</h5>
                    <ul class="list-unstyled">
                        <li><a href="/manage/branches" class="text-decoration-none text-muted"><i class="mdi mdi-chevron-left me-1"></i>إدارة الفروع</a></li>
                        <li><a href="/manage/drugs" class="text-decoration-none text-muted"><i class="mdi mdi-chevron-left me-1"></i>إدارة الأدوية</a></li>
                        <li><a href="/manage/drug_categories" class="text-decoration-none text-muted"><i class="mdi mdi-chevron-left me-1"></i>تصنيفات الأدوية</a></li>
                        <li><a href="/manage/drug_groups" class="text-decoration-none text-muted"><i class="mdi mdi-chevron-left me-1"></i>المجموعات الدوائية</a></li>
                        <li><a href="/manage/drug_group_codes" class="text-decoration-none text-muted"><i class="mdi mdi-chevron-left me-1"></i>أكواد المجموعات</a></li>
                        <li><a href="/manage/insulin" class="text-decoration-none text-muted"><i class="mdi mdi-chevron-left me-1"></i>إدارة الأنسولين</a></li>
                        <li><a href="/insulin/dispense" class="text-decoration-none text-muted"><i class="mdi mdi-chevron-left me-1"></i>منصرف الأنسولين</a></li>
                    </ul>
                </div>
            </div>
            <hr>
            <p class="mb-0 text-muted">
                تطبيق منصرف الأدوية &copy; 2024 |
                <a href="{{ url_for('copyright') }}" class="text-decoration-none text-muted">
                    <i class="mdi mdi-copyright me-1"></i>حقوق الملكية: ك/أحمد علي أحمد (أحمد كوكب)
                </a>
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    {% block scripts %}{% endblock %}
</body>
</html>
