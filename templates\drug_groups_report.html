{% extends "base.html" %}

{% block title %}تقرير المجموعات الدوائية - تطبيق منصرف الأدوية{% endblock %}

{% block content %}
<div class="container-fluid py-4" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); min-height: 100vh;">
    <!-- أزرار الطباعة والتصدير في الأعلى -->
    <div class="row mb-3 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-light">
                        <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="exportToExcel()" title="تصدير إلى Excel">
                        <i class="mdi mdi-file-excel me-1"></i>تصدير Excel
                    </button>
                    <button class="btn btn-info" onclick="showPrintOptions('pdf')" title="حفظ كـ PDF">
                        <i class="mdi mdi-file-pdf me-1"></i>حفظ PDF
                    </button>
                    <button class="btn btn-primary" onclick="showPrintOptions('print')" title="طباعة التقرير">
                        <i class="mdi mdi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-12">
            <!-- الشعار -->
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo" style="max-width: 150px; height: auto;">
            </div>

            <!-- Header Card -->
            <div class="card shadow-lg mb-4" style="border: none; border-radius: 20px; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
                <div class="card-header text-white text-center py-4" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); border-radius: 20px 20px 0 0; border: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div></div>
                        <div class="text-center">
                            <i class="mdi mdi-package-variant" style="font-size: 3rem; margin-bottom: 10px;"></i>
                            <h2 class="mb-0 fw-bold">تقرير المجموعات الدوائية</h2>
                            <p class="mb-0 opacity-75">تحليل شامل للمجموعات الدوائية والتكاليف</p>
                        </div>
                        <div class="no-print">
                            <span class="badge bg-light text-dark">
                                <i class="mdi mdi-calendar-range me-1"></i>{{ date_range_text }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات التقرير -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card border-0 bg-light rounded-3 p-3">
                                <h5 class="text-primary mb-3">
                                    <i class="mdi mdi-information-outline me-2"></i>معلومات التقرير
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-2"><i class="mdi mdi-calendar text-success me-2"></i><strong>تاريخ التقرير:</strong> {{ now.strftime('%Y-%m-%d %H:%M') }}</p>
                                        <p class="mb-2"><i class="mdi mdi-calendar-range text-info me-2"></i><strong>الشهر:</strong> {{ month }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-2"><i class="mdi mdi-map-marker text-warning me-2"></i><strong>النطاق:</strong> {{ scope_name if scope_name else scope_type }}</p>
                                        <p class="mb-0"><i class="mdi mdi-package-variant text-danger me-2"></i><strong>عدد المجموعات:</strong> {{ total_groups }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-0 bg-gradient text-white rounded-3 p-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <h6 class="text-white-50 mb-2">الملخص المالي</h6>
                                <h4 class="mb-1">{{ "{:,.2f}".format(total_cost) }} ج.م</h4>
                                <small class="text-white-75">إجمالي التكلفة</small>
                                <hr class="my-2 border-white-50">
                                <div class="d-flex justify-content-between">
                                    <span>المجموعات:</span>
                                    <span>{{ total_groups }}</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>متوسط التكلفة:</span>
                                    <span>{{ "{:,.2f}".format(total_cost / total_groups if total_groups > 0 else 0) }} ج.م</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <div class="card-body text-white text-center">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-3">
                                            <i class="mdi mdi-package-variant" style="font-size: 2.5rem;"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0">{{ total_groups }}</h3>
                                            <p class="mb-0">مجموعة دوائية</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                                <div class="card-body text-white text-center">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-3">
                                            <i class="mdi mdi-cash-multiple" style="font-size: 2.5rem;"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0">{{ "{:,.2f}".format(total_cost) }}</h3>
                                            <p class="mb-0">جنيه</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                                <div class="card-body text-center">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-3">
                                            <i class="mdi mdi-calculator text-primary" style="font-size: 2.5rem;"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0 text-dark">{{ "{:,.2f}".format(total_cost / total_groups if total_groups > 0 else 0) }}</h3>
                                            <p class="mb-0 text-muted">متوسط التكلفة</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                                <div class="card-body text-white text-center">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-3">
                                            <i class="mdi mdi-calendar-range" style="font-size: 2.5rem;"></i>
                                        </div>
                                        <div>
                                            <h4 class="mb-0">{{ month }}</h4>
                                            <p class="mb-0">الشهر</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المجموعات الدوائية -->
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="mdi mdi-format-list-bulleted me-2"></i>تفاصيل المجموعات الدوائية
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if groups_data %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead style="background: linear-gradient(135deg, #2c3e50, #34495e); color: white;">
                                        <tr>
                                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 50px; text-align: center;">
                                                <i class="mdi mdi-numeric me-1" style="color: #f1c40f;"></i>
                                                <span>#</span>
                                            </th>
                                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 200px;">
                                                <i class="mdi mdi-package-variant me-2" style="color: #f39c12;"></i>
                                                <span>اسم المجموعة الدوائية</span>
                                            </th>
                                            <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 120px;">
                                                <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                                <span>التكلفة الإجمالية</span><br>
                                                <small style="color: #ecf0f1; font-size: 11px;">(بالجنيه المصري)</small>
                                            </th>
                                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 150px;">
                                                <i class="mdi mdi-hospital-building me-2" style="color: #2ecc71;"></i>
                                                <span>العيادة</span>
                                            </th>
                                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #9b59b6; min-width: 120px;">
                                                <i class="mdi mdi-map-marker me-2" style="color: #e74c3c;"></i>
                                                <span>المنطقة</span>
                                            </th>
                                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #17a2b8; min-width: 120px;">
                                                <i class="mdi mdi-office-building me-2" style="color: #fd7e14;"></i>
                                                <span>الفرع</span>
                                            </th>
                                            <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #ffc107; min-width: 120px;">
                                                <i class="mdi mdi-calendar me-2" style="color: #28a745;"></i>
                                                <span>شهر الصرف</span>
                                            </th>
                                            <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #6f42c1; min-width: 120px;">
                                                <i class="mdi mdi-barcode me-2" style="color: #20c997;"></i>
                                                <span>كود المجموعة</span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for group in groups_data %}
                                        <tr>
                                            <td class="text-center">{{ loop.index }}</td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="mdi mdi-package-variant text-primary me-2"></i>
                                                    <strong>{{ group.group_name }}</strong>
                                                </div>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-success" style="font-size: 0.9rem; padding: 8px 12px;">
                                                    {{ "{:,.2f}".format(group.cost) }} ج.م
                                                </span>
                                            </td>
                                            <td>
                                                <i class="mdi mdi-hospital-building text-info me-1"></i>
                                                {{ group.clinic_name }}
                                            </td>
                                            <td>
                                                <i class="mdi mdi-map-marker text-warning me-1"></i>
                                                {{ group.area_name }}
                                            </td>
                                            <td>
                                                <i class="mdi mdi-office-building text-secondary me-1"></i>
                                                {{ group.branch_name }}
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-info">{{ group.dispense_month }}</span>
                                            </td>
                                            <td class="text-center">
                                                {% if group.code_name %}
                                                    <span class="badge bg-primary">{{ group.code_value }} - {{ group.code_name }}</span>
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                        <tr>
                                            <th colspan="2" class="text-start" style="border: none; font-weight: bold; padding: 15px;">
                                                <i class="mdi mdi-sigma me-1"></i>الإجمالي العام
                                            </th>
                                            <th class="text-center" style="border: none; font-weight: bold; color: #28a745; padding: 15px;">
                                                {{ "{:,.2f}".format(total_cost) }} ج.م
                                            </th>
                                            <th colspan="5" class="text-center" style="border: none; font-weight: bold; color: #6c757d; padding: 15px;">
                                                {{ total_groups }} مجموعة دوائية
                                            </th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="mdi mdi-information me-3" style="font-size: 2rem;"></i>
                                <strong>لا توجد مجموعات دوائية في الفترة المحددة.</strong>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- إحصائيات إضافية -->
                    {% if groups_data %}
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">متوسط التكلفة</h6>
                                    <h4 class="text-primary">{{ "{:,.2f}".format(total_cost / total_groups) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">أعلى تكلفة</h6>
                                    <h4 class="text-success">{{ "{:,.2f}".format(groups_data[0].cost) }} ج.م</h4>
                                    <small class="text-muted">{{ groups_data[0].group_name }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مربع حوار خيارات الطباعة -->
<div id="printOptionsModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">خيارات الطباعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">اتجاه الصفحة:</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="pageOrientation" id="orientationLandscape" value="landscape" checked>
                        <label class="form-check-label" for="orientationLandscape">أفقي (Landscape)</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="pageOrientation" id="orientationPortrait" value="portrait">
                        <label class="form-check-label" for="orientationPortrait">رأسي (Portrait)</label>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">حجم الخط:</label>
                    <select class="form-select" id="fontSize">
                        <option value="small">صغير</option>
                        <option value="medium" selected>متوسط</option>
                        <option value="large">كبير</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmPrint">طباعة</button>
            </div>
        </div>
    </div>
</div>

<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    var printAction = 'print';
    var printModal;

    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل صفحة تقرير المجموعات الدوائية بنجاح');
    });

    function showPrintOptions(action) {
        printAction = action;
        if (!printModal) {
            printModal = new bootstrap.Modal(document.getElementById('printOptionsModal'));
            document.getElementById('confirmPrint').addEventListener('click', function() {
                printModal.hide();
                performPrint();
            });
        }
        printModal.show();
    }

    function performPrint() {
        try {
            var orientation = document.querySelector('input[name="pageOrientation"]:checked').value;
            var fontSize = document.getElementById('fontSize').value;

            var styleElement = document.createElement('style');
            styleElement.id = 'print-orientation-style';
            styleElement.innerHTML = '@page { size: ' + orientation + '; }';

            var fontSizeMap = {
                'small': '9pt',
                'medium': '11pt',
                'large': '13pt'
            };
            styleElement.innerHTML += '.table { font-size: ' + fontSizeMap[fontSize] + '; }';

            document.head.appendChild(styleElement);
            window.print();

            setTimeout(function() {
                document.head.removeChild(styleElement);
            }, 1000);
        } catch (error) {
            console.error("Error printing:", error);
            alert("حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.");
        }
    }

    function exportToExcel() {
        var table = document.querySelector('.table');
        if (!table) return;

        var data = [];
        var headers = [];
        table.querySelectorAll('thead th').forEach(function(th) {
            headers.push(th.innerText.replace(/\n/g, ' ').trim());
        });
        data.push(headers);

        table.querySelectorAll('tbody tr').forEach(function(tr) {
            var row = [];
            tr.querySelectorAll('td').forEach(function(td) {
                row.push(td.innerText.replace(/\n/g, ' ').trim());
            });
            data.push(row);
        });

        var footerRow = [];
        table.querySelectorAll('tfoot tr').forEach(function(tr) {
            tr.querySelectorAll('th, td').forEach(function(cell) {
                footerRow.push(cell.innerText.replace(/\n/g, ' ').trim());
            });
        });
        if (footerRow.length > 0) {
            data.push(footerRow);
        }

        var ws = XLSX.utils.aoa_to_sheet(data);
        var wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "تقرير المجموعات الدوائية");

        var fileName = "تقرير_المجموعات_الدوائية_" + new Date().toISOString().slice(0, 10) + ".xlsx";
        XLSX.writeFile(wb, fileName);
    }
</script>

<style>
    .report-logo {
        max-width: 150px;
        height: auto;
        margin: 0 auto 15px;
        display: block;
    }

    @media print {
        .btn, .no-print {
            display: none !important;
        }

        .card {
            border: 1px solid #ddd !important;
            box-shadow: none !important;
            break-inside: avoid;
        }

        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
        }

        .table {
            font-size: 12px;
        }

        .container-fluid {
            padding: 0;
            background: white !important;
        }

        /* إضافة اللوجو في بداية صفحة الطباعة */
        body::before {
            content: '';
            display: block;
            background-image: url("{{ url_for('static', filename='images/logo.png') }}");
            background-repeat: no-repeat;
            background-position: center top;
            background-size: 150px auto;
            height: 100px;
            margin-bottom: 20px;
        }

        /* إضافة حقوق الملكية في نهاية صفحة الطباعة */
        body::after {
            content: 'جميع الحقوق محفوظة لـ ك/أحمد علي أحمد (أحمد كوكب) © {{ current_year }}';
            display: block;
            text-align: center;
            font-size: 10px;
            color: #666;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
    }
</style>
{% endblock %}
