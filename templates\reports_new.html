{% extends "base.html" %}

{% block title %}التقارير - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
/* Report Cards Gradients */
.gradient-purple { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.gradient-green { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
.gradient-blue { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.gradient-pink { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
.gradient-red { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.gradient-light { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
.gradient-yellow { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }
.gradient-cyan { background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%); }

/* Card Hover Effects */
.report-card {
    transform: translateY(0);
    transition: all 0.3s ease;
}
.report-card:hover {
    transform: translateY(-5px);
}

/* Icon Containers */
.icon-container-white {
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}
.icon-container-dark {
    background: rgba(0,0,0,0.1);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Icon Sizes */
.icon-3rem { font-size: 3rem; }
.icon-1-5rem { font-size: 1.5rem; }

/* Button Styles */
.btn-rounded {
    border-radius: 25px;
    padding: 10px 30px;
}

/* Hidden Elements */
.hidden-element { display: none; }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="mdi mdi-chart-bar me-2"></i>التقارير
                </h4>
            </div>
            <div class="card-body">
                <!-- زر التصدير الشامل -->
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            <h5 class="mb-3">
                                <i class="mdi mdi-microsoft-excel me-2"></i>تصدير شامل لجميع التقارير
                            </h5>
                            <p class="mb-3">احصل على جميع التقارير في ملف إكسل واحد مع صفحات منفصلة لكل تقرير</p>
                            <a href="{{ url_for('export_all_reports_excel') }}" class="btn btn-success btn-lg">
                                <i class="mdi mdi-download me-2"></i>تحميل التقرير الشامل (Excel)
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- تقرير شهري -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-lg border-0 report-card gradient-purple">
                            <div class="card-body text-center text-white">
                                <div class="mb-3 icon-container-white">
                                    <i class="mdi mdi-calendar-month icon-3rem text-white"></i>
                                </div>
                                <h5 class="mt-3 fw-bold">تقرير شهري</h5>
                                <p class="text-white-50">عرض تقرير مفصل للمنصرف الشهري لكل منطقة</p>
                                <button type="button" class="btn btn-light btn-lg fw-bold btn-rounded" data-bs-toggle="modal" data-bs-target="#monthlyReportModal">
                                    <i class="mdi mdi-chart-line me-2"></i>عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير الأدوية -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-lg border-0 report-card gradient-green">
                            <div class="card-body text-center text-white">
                                <div class="mb-3 icon-container-white">
                                    <i class="mdi mdi-pill icon-3rem text-white"></i>
                                </div>
                                <h5 class="mt-3 fw-bold">تقرير الأدوية</h5>
                                <p class="text-white-50">عرض تقرير مفصل عن استهلاك الأدوية</p>
                                <button type="button" class="btn btn-light btn-lg fw-bold btn-rounded" data-bs-toggle="modal" data-bs-target="#drugsReportModal">
                                    <i class="mdi mdi-pill me-2"></i>عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير العيادات -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-lg border-0 report-card gradient-blue">
                            <div class="card-body text-center text-white">
                                <div class="mb-3 icon-container-white">
                                    <i class="mdi mdi-hospital-building icon-3rem text-white"></i>
                                </div>
                                <h5 class="mt-3 fw-bold">تقرير العيادات</h5>
                                <p class="text-white-50">عرض تقرير مفصل عن المنصرف لكل عيادة</p>
                                <button type="button" class="btn btn-light btn-lg fw-bold btn-rounded" data-bs-toggle="modal" data-bs-target="#clinicsReportModal">
                                    <i class="mdi mdi-hospital-building me-2"></i>عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير التكلفة -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-lg border-0 report-card gradient-pink">
                            <div class="card-body text-center text-white">
                                <div class="mb-3 icon-container-white">
                                    <i class="mdi mdi-cash-multiple icon-3rem text-white"></i>
                                </div>
                                <h5 class="mt-3 fw-bold">تقرير التكلفة</h5>
                                <p class="text-white-50">عرض تقرير مفصل عن تكلفة المنصرف</p>
                                <button type="button" class="btn btn-light btn-lg fw-bold btn-rounded" data-bs-toggle="modal" data-bs-target="#costReportModal">
                                    <i class="mdi mdi-calculator me-2"></i>عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير الأنسولين المجمع -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-lg border-0 report-card gradient-red">
                            <div class="card-body text-center text-white">
                                <div class="mb-3 icon-container-white">
                                    <i class="mdi mdi-needle icon-3rem text-white"></i>
                                </div>
                                <h5 class="mt-3 fw-bold">تقرير الأنسولين المجمع</h5>
                                <p class="text-white-50">عرض جداول منفصلة لكل فئة ونوع</p>
                                <button type="button" class="btn btn-light btn-lg fw-bold btn-rounded" data-bs-toggle="modal" data-bs-target="#insulinReportModal">
                                    <i class="mdi mdi-needle me-2"></i>عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير الأنسولين المحسن -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-lg border-0" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); transform: translateY(0); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                            <div class="card-body text-center text-white">
                                <div class="mb-3" style="background: rgba(255,255,255,0.2); border-radius: 50%; width: 80px; height: 80px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                                    <i class="mdi mdi-medical-bag" style="font-size: 3rem; color: white;"></i>
                                </div>
                                <h5 class="mt-3 fw-bold">تقرير الأنسولين المحسن</h5>
                                <p class="text-white-50">تصفية مرنة حسب فئة أو نوع محدد</p>
                                <a href="/reports/insulin-enhanced" target="_blank" class="btn btn-light btn-lg fw-bold" style="border-radius: 25px; padding: 10px 30px;">
                                    <i class="mdi mdi-filter me-2"></i>عرض التقرير
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير المقارنة -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-lg border-0" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); transform: translateY(0); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                            <div class="card-body text-center text-dark">
                                <div class="mb-3" style="background: rgba(0,0,0,0.1); border-radius: 50%; width: 80px; height: 80px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                                    <i class="mdi mdi-compare" style="font-size: 3rem; color: #333;"></i>
                                </div>
                                <h5 class="mt-3 fw-bold text-dark">تقرير المقارنة</h5>
                                <p class="text-muted">مقارنة بين الفروع والمناطق والعيادات</p>
                                <button type="button" class="btn btn-dark btn-lg fw-bold" data-bs-toggle="modal" data-bs-target="#comparisonReportModal" style="border-radius: 25px; padding: 10px 30px;">
                                    <i class="mdi mdi-compare me-2"></i>عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير المجموعات الدوائية -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-lg border-0" style="background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); transform: translateY(0); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                            <div class="card-body text-center text-dark">
                                <div class="mb-3" style="background: rgba(0,0,0,0.1); border-radius: 50%; width: 80px; height: 80px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                                    <i class="mdi mdi-package-variant" style="font-size: 3rem; color: #333;"></i>
                                </div>
                                <h5 class="mt-3 fw-bold text-dark">المجموعات الدوائية</h5>
                                <p class="text-muted">عرض تقرير تكلفة المجموعات الدوائية</p>
                                <button type="button" class="btn btn-dark btn-lg fw-bold" data-bs-toggle="modal" data-bs-target="#drugGroupsReportModal" style="border-radius: 25px; padding: 10px 30px;">
                                    <i class="mdi mdi-package-variant me-2"></i>عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير بسيط -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-lg border-0" style="background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%); transform: translateY(0); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                            <div class="card-body text-center text-white">
                                <div class="mb-3" style="background: rgba(255,255,255,0.2); border-radius: 50%; width: 80px; height: 80px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                                    <i class="mdi mdi-file-document" style="font-size: 3rem; color: white;"></i>
                                </div>
                                <h5 class="mt-3 fw-bold">تقرير بسيط</h5>
                                <p class="text-white-50">عرض تقرير مبسط للمنصرف اليومي</p>
                                <button type="button" class="btn btn-light btn-lg fw-bold" data-bs-toggle="modal" data-bs-target="#simpleReportModal" style="border-radius: 25px; padding: 10px 30px;">
                                    <i class="mdi mdi-file-document me-2"></i>عرض التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal: تقرير شهري -->
<div class="modal fade" id="monthlyReportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">تقرير شهري</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info mb-3">
                    <i class="mdi mdi-information me-2"></i>
                    <strong>تقرير إداري شامل</strong> - إحصائيات شهرية على مستوى الفروع
                </div>
                <form action="/reports/monthly" method="GET" target="_blank">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="month" class="form-label">
                                <i class="mdi mdi-calendar me-1"></i>الشهر المطلوب
                            </label>
                            <input type="month" class="form-control" id="month" name="month" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="monthly_scope" class="form-label">
                                <i class="mdi mdi-office-building me-1"></i>نطاق التقرير
                            </label>
                            <select class="form-select" id="monthly_scope" name="scope_type">
                                <option value="all">📊 جميع الفروع (تقرير شامل)</option>
                                <option value="branch">🏢 فرع محدد</option>
                                <option value="branch_areas">🏘️ جميع مناطق الفرع</option>
                                <option value="area">📍 منطقة محددة</option>
                            </select>
                        </div>
                    </div>
                    <div class="row hidden-element" id="monthly_location_selectors">
                        <div class="col-md-6 mb-3" id="monthly_branch_container">
                            <label for="monthly_branch_id" class="form-label">اختر الفرع</label>
                            <select class="form-select" id="monthly_branch_id" name="branch_id">
                                <option value="">-- اختر الفرع --</option>
                                {% for branch in branches %}
                                <option value="{{ branch.id }}">{{ branch.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3 hidden-element" id="monthly_area_container">
                            <label for="monthly_area_id" class="form-label">اختر المنطقة</label>
                            <select class="form-select" id="monthly_area_id" name="area_id">
                                <option value="">-- اختر المنطقة --</option>
                            </select>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="mdi mdi-chart-line me-2"></i>إنتاج التقرير الشهري
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal: تقرير الأدوية -->
<div class="modal fade" id="drugsReportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">تقرير الأدوية</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-success mb-3">
                    <i class="mdi mdi-pill me-2"></i>
                    <strong>تقرير تحليلي مفصل</strong> - يجمع الأدوية حسب الأسعار المتساوية مع عرض الكميات والتكلفة
                </div>
                <form action="/reports/drugs" method="GET" target="_blank">
                    <!-- الإعدادات الأساسية -->
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="mdi mdi-cog me-1"></i>إعدادات التقرير الأساسية</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="drug_category" class="form-label">
                                        <i class="mdi mdi-tag me-1"></i>تصنيف الأدوية
                                    </label>
                                    <select class="form-select" id="drug_category" name="category_id">
                                        <option value="">🏷️ جميع التصنيفات</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}">{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="date_range" class="form-label">
                                        <i class="mdi mdi-calendar-range me-1"></i>الفترة الزمنية
                                    </label>
                                    <select class="form-select" id="date_range" name="date_range">
                                        <option value="month">📅 الشهر الحالي</option>
                                        <option value="quarter">📊 الربع الحالي</option>
                                        <option value="year">📈 السنة الحالية</option>
                                        <option value="custom">🗓️ فترة مخصصة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row custom-date-range" style="display: none;">
                                <div class="col-md-6 mb-3">
                                    <label for="start_date" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="end_date" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نطاق التقرير -->
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="mdi mdi-map-marker me-1"></i>نطاق التقرير الجغرافي</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="drugs_scope_type" class="form-label">اختر النطاق</label>
                                    <select class="form-select" id="drugs_scope_type" name="scope_type">
                                        <option value="all">🌍 جميع الفروع (تحليل شامل)</option>
                                        <option value="branch">🏢 فرع محدد</option>
                                        <option value="area">🏘️ منطقة محددة</option>
                                        <option value="clinic">🏥 عيادة محددة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row" id="drugs_location_selectors" style="display: none;">
                                <div class="col-md-4 mb-3" id="drugs_branch_container">
                                    <label for="drugs_branch_id" class="form-label">الفرع</label>
                                    <select class="form-select" id="drugs_branch_id" name="branch_id">
                                        <option value="">-- اختر الفرع --</option>
                                        {% for branch in branches %}
                                        <option value="{{ branch.id }}">{{ branch.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3" id="drugs_area_container" style="display: none;">
                                    <label for="drugs_area_id" class="form-label">المنطقة</label>
                                    <select class="form-select" id="drugs_area_id" name="area_id">
                                        <option value="">-- اختر المنطقة --</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3" id="drugs_clinic_container" style="display: none;">
                                    <label for="drugs_clinic_id" class="form-label">العيادة</label>
                                    <select class="form-select" id="drugs_clinic_id" name="clinic_id">
                                        <option value="">-- اختر العيادة --</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظة مهمة -->
                    <div class="alert alert-warning mb-3">
                        <i class="mdi mdi-lightbulb me-2"></i>
                        <strong>ملاحظة:</strong> سيتم جمع الأدوية المتشابهة حسب الأسعار المتساوية وعرض إجمالي الكميات والتكلفة لكل مجموعة سعرية
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="mdi mdi-chart-bar me-2"></i>إنتاج تقرير الأدوية المفصل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal: تقرير العيادات -->
<div class="modal fade" id="clinicsReportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">تقرير العيادات</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info mb-3">
                    <i class="mdi mdi-hospital-building me-2"></i>
                    <strong>تقرير أداء العيادات</strong> - تقييم مباشر لأداء العيادات الفردية
                </div>
                <form action="/reports/clinics" method="GET" target="_blank">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="clinic_month" class="form-label">
                                <i class="mdi mdi-calendar me-1"></i>الشهر المطلوب
                            </label>
                            <input type="month" class="form-control" id="clinic_month" name="month" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="clinics_report_type" class="form-label">
                                <i class="mdi mdi-view-list me-1"></i>نوع التقرير
                            </label>
                            <select class="form-select" id="clinics_report_type" name="report_type">
                                <option value="all">📊 جميع العيادات (مقارنة شاملة)</option>
                                <option value="single">🏥 عيادة محددة (تقرير مفصل)</option>
                            </select>
                        </div>
                    </div>

                    <div class="row" id="clinic_selector_row" style="display: none;">
                        <div class="col-md-12 mb-3">
                            <label for="clinic_select" class="form-label">
                                <i class="mdi mdi-hospital-marker me-1"></i>اختر العيادة
                            </label>
                            <select class="form-select" id="clinic_select" name="clinic_id">
                                <option value="">-- اختر العيادة --</option>
                                {% for clinic in clinics %}
                                <option value="{{ clinic.id }}">
                                    🏥 {{ clinic.name }} - {{ clinic.area_name }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                <i class="mdi mdi-information me-1"></i>
                                يمكنك البحث عن العيادة بالاسم أو المنطقة
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-info btn-lg">
                            <i class="mdi mdi-hospital-building me-2"></i>إنتاج تقرير العيادات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal: تقرير التكلفة -->
<div class="modal fade" id="costReportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">تقرير التكلفة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning mb-3">
                    <i class="mdi mdi-cash-multiple me-2"></i>
                    <strong>تقرير مالي استراتيجي</strong> - تحليل التكلفة السنوية للمساعدة في اتخاذ القرارات المالية
                </div>
                <form action="/reports/cost" method="GET" target="_blank">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cost_year" class="form-label">
                                <i class="mdi mdi-calendar me-1"></i>السنة المالية
                            </label>
                            <select class="form-select" id="cost_year" name="year" required>
                                <option value="{{ now.year }}">{{ now.year }} (السنة الحالية)</option>
                                <option value="{{ now.year - 1 }}">{{ now.year - 1 }}</option>
                                <option value="{{ now.year - 2 }}">{{ now.year - 2 }}</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="cost_analysis_type" class="form-label">
                                <i class="mdi mdi-chart-pie me-1"></i>نوع التحليل
                            </label>
                            <select class="form-select" id="cost_analysis_type" name="analysis_type">
                                <option value="summary">📈 تقرير إجمالي (جميع الفروع)</option>
                                <option value="comparison">⚖️ مقارنة بين الفروع</option>
                                <option value="detailed">🔍 تحليل مفصل لفرع محدد</option>
                            </select>
                        </div>
                    </div>

                    <div class="row" id="cost_branch_row" style="display: none;">
                        <div class="col-md-12 mb-3">
                            <label for="cost_branch_select" class="form-label">
                                <i class="mdi mdi-office-building me-1"></i>اختر الفرع للتحليل المفصل
                            </label>
                            <select class="form-select" id="cost_branch_select" name="branch_id">
                                <option value="">-- اختر الفرع --</option>
                                {% for branch in branches %}
                                <option value="{{ branch.id }}">{{ branch.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="mdi mdi-information me-1"></i>معلومات التقرير
                            </h6>
                            <ul class="mb-0 small">
                                <li><strong>التقرير الإجمالي:</strong> عرض إجمالي التكلفة لجميع الفروع</li>
                                <li><strong>المقارنة:</strong> مقارنة التكلفة بين الفروع مع النسب المئوية</li>
                                <li><strong>التحليل المفصل:</strong> تفصيل التكلفة حسب المناطق والعيادات داخل الفرع</li>
                            </ul>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-warning btn-lg">
                            <i class="mdi mdi-calculator me-2"></i>إنتاج التقرير المالي
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal: تقرير الأنسولين -->
<div class="modal fade" id="insulinReportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">تقرير الأنسولين المجمع (حسب الفئة والنوع)</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form action="/reports/insulin-grouped" method="GET" target="_blank">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="insulin_scope_type" class="form-label">نطاق التقرير</label>
                            <select class="form-select" id="insulin_scope_type" name="scope_type">
                                <option value="all">جميع الفروع</option>
                                <option value="branch">فرع محدد</option>
                                <option value="area">منطقة محددة</option>
                                <option value="clinic">عيادة محددة</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="insulin_date_range" class="form-label">الفترة الزمنية</label>
                            <select class="form-select" id="insulin_date_range" name="date_range">
                                <option value="month">الشهر الحالي</option>
                                <option value="quarter">الربع الحالي</option>
                                <option value="q1">الربع الأول (يناير - مارس)</option>
                                <option value="q2">الربع الثاني (أبريل - يونيو)</option>
                                <option value="q3">الربع الثالث (يوليو - سبتمبر)</option>
                                <option value="q4">الربع الرابع (أكتوبر - ديسمبر)</option>
                                <option value="year">السنة الحالية</option>
                                <option value="custom">فترة مخصصة</option>
                            </select>
                        </div>
                    </div>

                    <!-- حقول التاريخ المخصصة -->
                    <div class="row custom-date-range" style="display: none;">
                        <div class="col-md-6 mb-3">
                            <label for="insulin_start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="insulin_start_date" name="start_date">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="insulin_end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="insulin_end_date" name="end_date">
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="mdi mdi-information me-2"></i>
                        <strong>ملاحظة:</strong> سيتم عرض جميع الفئات والأنواع المتوفرة في جداول منفصلة
                    </div>
                    <div class="row" id="insulin_location_selectors" style="display: none;">
                        <div class="col-md-6 mb-3" id="insulin_branch_container">
                            <label for="insulin_branch_id" class="form-label">الفرع</label>
                            <select class="form-select" id="insulin_branch_id" name="branch_id">
                                <option value="">-- اختر الفرع --</option>
                                {% for branch in branches %}
                                <option value="{{ branch.id }}">{{ branch.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3" id="insulin_area_container" style="display: none;">
                            <label for="insulin_area_id" class="form-label">المنطقة</label>
                            <select class="form-select" id="insulin_area_id" name="area_id">
                                <option value="">-- اختر المنطقة --</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3" id="insulin_clinic_container" style="display: none;">
                            <label for="insulin_clinic_id" class="form-label">العيادة</label>
                            <select class="form-select" id="insulin_clinic_id" name="clinic_id">
                                <option value="">-- اختر العيادة --</option>
                            </select>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-danger">
                            <i class="mdi mdi-file-document me-1"></i>عرض التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal: تقرير المقارنة -->
<div class="modal fade" id="comparisonReportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-secondary text-white">
                <h5 class="modal-title">تقرير المقارنة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form action="/reports/comparison" method="GET" target="_blank">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="comparison_scope_type" class="form-label">نوع المقارنة</label>
                            <select class="form-select" id="comparison_scope_type" name="scope_type">
                                <option value="all">مقارنة بين الفروع</option>
                                <option value="branch">مقارنة بين المناطق في فرع</option>
                                <option value="area">مقارنة بين العيادات في منطقة</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="comparison_date_range" class="form-label">الفترة الزمنية</label>
                            <select class="form-select" id="comparison_date_range" name="date_range">
                                <option value="month">الشهر الحالي</option>
                                <option value="quarter">الربع الحالي</option>
                                <option value="year">السنة الحالية</option>
                                <option value="custom">فترة مخصصة</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3" id="comparison_parent_container" style="display: none;">
                            <label for="comparison_parent_id" class="form-label">اختر الفرع/المنطقة</label>
                            <select class="form-select" id="comparison_parent_id" name="parent_id">
                                <option value="">-- اختر --</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="comparison_category" class="form-label">تصنيف الدواء</label>
                            <select class="form-select" id="comparison_category" name="category_id">
                                <option value="">-- جميع التصنيفات --</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-secondary">
                            <i class="mdi mdi-file-document me-1"></i>عرض التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal: تقرير المجموعات الدوائية -->
<div class="modal fade" id="drugGroupsReportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-dark text-white">
                <h5 class="modal-title">تقرير المجموعات الدوائية</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form action="/reports/drug_groups" method="GET" target="_blank">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="drug_groups_scope_type" class="form-label">نطاق التقرير</label>
                            <select class="form-select" id="drug_groups_scope_type" name="scope_type">
                                <option value="all">جميع الفروع</option>
                                <option value="branch">فرع محدد</option>
                                <option value="area">منطقة محددة</option>
                                <option value="clinic">عيادة محددة</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="drug_groups_month" class="form-label">الشهر</label>
                            <input type="month" class="form-control" id="drug_groups_month" name="month" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3" id="drug_groups_branch_container" style="display: none;">
                            <label for="drug_groups_branch_id" class="form-label">الفرع</label>
                            <select class="form-select" id="drug_groups_branch_id" name="branch_id">
                                <option value="">-- اختر الفرع --</option>
                                {% for branch in branches %}
                                <option value="{{ branch.id }}">{{ branch.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3" id="drug_groups_area_container" style="display: none;">
                            <label for="drug_groups_area_id" class="form-label">المنطقة</label>
                            <select class="form-select" id="drug_groups_area_id" name="area_id">
                                <option value="">-- اختر المنطقة --</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3" id="drug_groups_clinic_container" style="display: none;">
                            <label for="drug_groups_clinic_id" class="form-label">العيادة</label>
                            <select class="form-select" id="drug_groups_clinic_id" name="clinic_id">
                                <option value="">-- اختر العيادة --</option>
                            </select>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-dark">
                            <i class="mdi mdi-file-document me-1"></i>عرض التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal: التقرير البسيط -->
<div class="modal fade" id="simpleReportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="mdi mdi-file-document me-2"></i>التقرير البسيط
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info mb-4">
                    <i class="mdi mdi-information-outline me-2" style="font-size: 1.5rem;"></i>
                    <strong>التقرير البسيط</strong> - عرض سريع وشامل للمنصرف حسب النطاق والتاريخ المحدد
                </div>

                <form action="/simple_report" method="GET" target="_blank">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="simple_date" class="form-label">
                                <i class="mdi mdi-calendar text-primary me-1"></i>التاريخ المطلوب
                            </label>
                            <input type="date" class="form-control" id="simple_date" name="date" value="{{ now.strftime('%Y-%m-%d') if now else '' }}">
                            <small class="form-text text-muted">اتركه فارغاً لعرض جميع البيانات</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="simple_scope_type" class="form-label">
                                <i class="mdi mdi-office-building text-success me-1"></i>نطاق التقرير
                            </label>
                            <select class="form-select" id="simple_scope_type" name="scope_type">
                                <option value="all">📊 جميع الفروع والمناطق</option>
                                <option value="branch">🏢 فرع محدد</option>
                                <option value="area">🏘️ منطقة محددة</option>
                                <option value="clinic">🏥 عيادة محددة</option>
                            </select>
                        </div>
                    </div>

                    <div class="row" id="simple_location_selectors" style="display: none;">
                        <div class="col-md-6 mb-3" id="simple_branch_container">
                            <label for="simple_branch_id" class="form-label">
                                <i class="mdi mdi-domain text-warning me-1"></i>اختر الفرع
                            </label>
                            <select class="form-select" id="simple_branch_id" name="branch_id">
                                <option value="">-- اختر الفرع --</option>
                                {% for branch in branches %}
                                <option value="{{ branch.id }}">{{ branch.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3" id="simple_area_container" style="display: none;">
                            <label for="simple_area_id" class="form-label">
                                <i class="mdi mdi-map-marker text-info me-1"></i>اختر المنطقة
                            </label>
                            <select class="form-select" id="simple_area_id" name="area_id">
                                <option value="">-- اختر المنطقة --</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3" id="simple_clinic_container" style="display: none;">
                            <label for="simple_clinic_id" class="form-label">
                                <i class="mdi mdi-hospital-building text-danger me-1"></i>اختر العيادة
                            </label>
                            <select class="form-select" id="simple_clinic_id" name="clinic_id">
                                <option value="">-- اختر العيادة --</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-muted">
                                        <i class="mdi mdi-information me-1"></i>معلومات التقرير:
                                    </h6>
                                    <ul class="mb-0 text-muted small">
                                        <li>يعرض إحصائيات عامة للنظام</li>
                                        <li>أهم الأدوية المنصرفة مع التكاليف</li>
                                        <li>إجمالي الكميات والتكاليف</li>
                                        <li>عدد الفروع والمناطق والعيادات</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-info btn-lg px-4">
                            <i class="mdi mdi-file-document-outline me-2"></i>عرض التقرير البسيط
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" data-bs-dismiss="modal">
                            <i class="mdi mdi-close me-1"></i>إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // تعيين التاريخ الحالي كقيمة افتراضية للشهر
        var today = new Date();
        $('#month, #clinic_month, #drug_groups_month').val(formatMonthInput(today));

        // إظهار/إخفاء حقول التاريخ المخصصة
        $('#date_range, #insulin_date_range, #comparison_date_range').change(function() {
            if ($(this).val() === 'custom') {
                $('.custom-date-range').show();
            } else {
                $('.custom-date-range').hide();
            }
        });

        // إظهار/إخفاء حقول التاريخ المخصصة للأنسولين تحديداً
        $('#insulin_date_range').change(function() {
            if ($(this).val() === 'custom') {
                $('.custom-date-range').show();
            } else {
                $('.custom-date-range').hide();
            }
        });

        // إغلاق النوافذ المنبثقة بعد إرسال النماذج
        $('form[target="_blank"]').on('submit', function() {
            // إغلاق النافذة المنبثقة بعد تأخير قصير للسماح بفتح التقرير
            setTimeout(function() {
                $('.modal').modal('hide');
            }, 500);
        });

        // التقرير الشهري - إظهار/إخفاء حقول الاختيار
        $('#monthly_scope').change(function() {
            var scopeType = $(this).val();

            // إخفاء جميع الحاويات أولاً
            $('#monthly_location_selectors').hide();
            $('#monthly_branch_container, #monthly_area_container').hide();

            if (scopeType === 'branch') {
                // فرع محدد - إظهار اختيار الفرع فقط
                $('#monthly_location_selectors').show();
                $('#monthly_branch_container').show();
            } else if (scopeType === 'branch_areas') {
                // جميع مناطق الفرع - إظهار اختيار الفرع فقط
                $('#monthly_location_selectors').show();
                $('#monthly_branch_container').show();
            } else if (scopeType === 'area') {
                // منطقة محددة - إظهار اختيار الفرع والمنطقة
                $('#monthly_location_selectors').show();
                $('#monthly_branch_container').show();
                $('#monthly_area_container').show();
            }
        });

        // التقرير الشهري - تحديث المناطق عند اختيار فرع
        $('#monthly_branch_id').change(function() {
            updateAreas($(this).val(), '#monthly_area_id');
        });

        // تقرير الأدوية - إظهار/إخفاء حقول الاختيار
        $('#drugs_scope_type').change(function() {
            var scopeType = $(this).val();

            if (scopeType === 'all') {
                $('#drugs_location_selectors').hide();
                $('#drugs_branch_container, #drugs_area_container, #drugs_clinic_container').hide();
            } else {
                $('#drugs_location_selectors').show();
                $('#drugs_branch_container').show();

                if (scopeType === 'area') {
                    $('#drugs_area_container').show();
                    $('#drugs_clinic_container').hide();
                } else if (scopeType === 'clinic') {
                    $('#drugs_area_container').show();
                    $('#drugs_clinic_container').show();
                } else {
                    $('#drugs_area_container, #drugs_clinic_container').hide();
                }
            }
        });

        // تقرير العيادات - إظهار/إخفاء حقول الاختيار
        $('#clinics_report_type').change(function() {
            var reportType = $(this).val();

            if (reportType === 'single') {
                $('#clinic_selector_row').show();
            } else {
                $('#clinic_selector_row').hide();
            }
        });

        // تقرير التكلفة - إظهار/إخفاء حقول الاختيار
        $('#cost_analysis_type').change(function() {
            var analysisType = $(this).val();

            if (analysisType === 'detailed') {
                $('#cost_branch_row').show();
            } else {
                $('#cost_branch_row').hide();
            }
        });

        // تقرير المقارنة - إظهار/إخفاء حقول الاختيار
        $('#comparison_scope_type').change(function() {
            var scopeType = $(this).val();
            var parentContainer = $('#comparison_parent_container');
            var parentSelect = $('#comparison_parent_id');

            if (scopeType === 'branch') {
                parentContainer.show();
                parentSelect.find('option').remove();
                parentSelect.append('<option value="">-- اختر الفرع --</option>');
                // إضافة الفروع من البيانات المرسلة من الخادم
                var branches = {{ branches|tojson }};
                branches.forEach(function(branch) {
                    parentSelect.append('<option value="' + branch.id + '">' + branch.name + '</option>');
                });
                parentContainer.find('label').text('اختر الفرع');
            } else if (scopeType === 'area') {
                parentContainer.show();
                parentSelect.find('option').remove();
                parentSelect.append('<option value="">-- اختر المنطقة --</option>');
                {% for area in areas %}
                parentSelect.append('<option value="{{ area.id }}">{{ area.name }} ({{ area.branch_name }})</option>');
                {% endfor %}
                parentContainer.find('label').text('اختر المنطقة');
            } else {
                parentContainer.hide();
            }
        });

        // تقرير الأنسولين - إظهار/إخفاء حقول الاختيار
        $('#insulin_scope_type').change(function() {
            var scopeType = $(this).val();

            if (scopeType === 'all') {
                $('#insulin_location_selectors').hide();
                $('#insulin_branch_container, #insulin_area_container, #insulin_clinic_container').hide();
            } else {
                $('#insulin_location_selectors').show();
                $('#insulin_branch_container').show();

                if (scopeType === 'area') {
                    $('#insulin_area_container').show();
                    $('#insulin_clinic_container').hide();
                } else if (scopeType === 'clinic') {
                    $('#insulin_area_container').show();
                    $('#insulin_clinic_container').show();
                } else {
                    $('#insulin_area_container, #insulin_clinic_container').hide();
                }
            }
        });

        // تقرير الأنسولين - تحديث القوائم
        $('#insulin_branch_id').change(function() {
            updateAreas($(this).val(), '#insulin_area_id');
            $('#insulin_clinic_id').find('option').remove();
            $('#insulin_clinic_id').append('<option value="">-- اختر العيادة --</option>');
        });

        $('#insulin_area_id').change(function() {
            updateClinics($(this).val(), '#insulin_clinic_id');
        });



        // تقرير المجموعات الدوائية - إظهار/إخفاء حقول الاختيار
        $('#drug_groups_scope_type').change(function() {
            var scopeType = $(this).val();

            // إخفاء جميع الحاويات أولاً
            $('#drug_groups_branch_container, #drug_groups_area_container, #drug_groups_clinic_container').hide();

            if (scopeType === 'branch') {
                $('#drug_groups_branch_container').show();
            } else if (scopeType === 'area') {
                $('#drug_groups_branch_container, #drug_groups_area_container').show();
            } else if (scopeType === 'clinic') {
                $('#drug_groups_branch_container, #drug_groups_area_container, #drug_groups_clinic_container').show();
            }
        });

        // تحديث المناطق عند اختيار فرع في تقرير المجموعات الدوائية
        $('#drug_groups_branch_id').change(function() {
            var branchId = $(this).val();
            var areaSelect = $('#drug_groups_area_id');

            areaSelect.find('option').remove();
            areaSelect.append('<option value="">-- اختر المنطقة --</option>');

            if (branchId) {
                {% for area in areas %}
                if ('{{ area.branch_id }}' === branchId) {
                    areaSelect.append('<option value="{{ area.id }}">{{ area.name }}</option>');
                }
                {% endfor %}
            }

            // إعادة تعيين العيادات
            $('#drug_groups_clinic_id').find('option').remove();
            $('#drug_groups_clinic_id').append('<option value="">-- اختر العيادة --</option>');
        });

        // تحديث العيادات عند اختيار منطقة في تقرير المجموعات الدوائية
        $('#drug_groups_area_id').change(function() {
            var areaId = $(this).val();
            var clinicSelect = $('#drug_groups_clinic_id');

            clinicSelect.find('option').remove();
            clinicSelect.append('<option value="">-- اختر العيادة --</option>');

            if (areaId) {
                {% for clinic in clinics %}
                if ('{{ clinic.area_id }}' === areaId) {
                    clinicSelect.append('<option value="{{ clinic.id }}">{{ clinic.name }}</option>');
                }
                {% endfor %}
            }
        });

        // دوال مساعدة لتحديث القوائم المنسدلة
        function updateAreas(branchId, areaSelectId) {
            var areaSelect = $(areaSelectId);
            areaSelect.find('option').remove();
            areaSelect.append('<option value="">-- اختر المنطقة --</option>');

            if (branchId) {
                {% for area in areas %}
                if ('{{ area.branch_id }}' === branchId) {
                    areaSelect.append('<option value="{{ area.id }}">{{ area.name }}</option>');
                }
                {% endfor %}
            }
        }

        function updateClinics(areaId, clinicSelectId) {
            var clinicSelect = $(clinicSelectId);
            clinicSelect.find('option').remove();
            clinicSelect.append('<option value="">-- اختر العيادة --</option>');

            if (areaId) {
                {% for clinic in clinics %}
                if ('{{ clinic.area_id }}' === areaId) {
                    clinicSelect.append('<option value="{{ clinic.id }}">{{ clinic.name }}</option>');
                }
                {% endfor %}
            }
        }

        // تقرير الأدوية - تحديث القوائم
        $('#drugs_branch_id').change(function() {
            updateAreas($(this).val(), '#drugs_area_id');
            $('#drugs_clinic_id').find('option').remove();
            $('#drugs_clinic_id').append('<option value="">-- اختر العيادة --</option>');
        });

        $('#drugs_area_id').change(function() {
            updateClinics($(this).val(), '#drugs_clinic_id');
        });

        // التقرير البسيط - إظهار/إخفاء حقول الاختيار
        $('#simple_scope_type').change(function() {
            var scopeType = $(this).val();

            if (scopeType === 'all') {
                $('#simple_location_selectors').hide();
                $('#simple_branch_container, #simple_area_container, #simple_clinic_container').hide();
            } else {
                $('#simple_location_selectors').show();
                $('#simple_branch_container').show();

                if (scopeType === 'area') {
                    $('#simple_area_container').show();
                    $('#simple_clinic_container').hide();
                } else if (scopeType === 'clinic') {
                    $('#simple_area_container').show();
                    $('#simple_clinic_container').show();
                } else {
                    $('#simple_area_container, #simple_clinic_container').hide();
                }
            }
        });

        // التقرير البسيط - تحديث القوائم
        $('#simple_branch_id').change(function() {
            updateAreas($(this).val(), '#simple_area_id');
            $('#simple_clinic_id').find('option').remove();
            $('#simple_clinic_id').append('<option value="">-- اختر العيادة --</option>');
        });

        $('#simple_area_id').change(function() {
            updateClinics($(this).val(), '#simple_clinic_id');
        });

        // تعيين التاريخ الحالي للتقرير البسيط
        var today = new Date();
        var todayForInput = today.getFullYear() + '-' +
                           String(today.getMonth() + 1).padStart(2, '0') + '-' +
                           String(today.getDate()).padStart(2, '0');
        $('#simple_date').val(todayForInput);
    });

    // تحديث JavaScript لاستخدام CSS classes بدلاً من inline styles
    function toggleLocationSelectors() {
        // استبدال style.display بـ classList
        const elements = document.querySelectorAll('[style*="display: none"]');
        elements.forEach(function(element) {
            if (element.style.display === 'none') {
                element.classList.add('hidden-element');
                element.style.display = '';
            }
        });
    }

    // تشغيل التحديث عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', toggleLocationSelectors);
</script>
{% endblock %}
