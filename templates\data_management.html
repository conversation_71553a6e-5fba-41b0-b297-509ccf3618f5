{% extends "base.html" %}

{% block title %}إدارة البيانات والنسخ الاحتياطية{% endblock %}

{% block content %}
<style>
.cursor-pointer {
    cursor: pointer;
}
.backup-checkbox:checked + label {
    background-color: #e3f2fd;
    border-radius: 4px;
    padding: 2px 4px;
}
</style>
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="mdi mdi-database-settings me-2"></i>
                            إدارة البيانات والنسخ الاحتياطية
                        </h4>
                        <a href="/" class="btn btn-light btn-sm">
                            <i class="mdi mdi-arrow-left me-1"></i>العودة للرئيسية
                        </a>
                    </div>
                </div>
                <div class="card-body p-4">
                    
                    <!-- إحصائيات قاعدة البيانات -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="mdi mdi-chart-bar me-2"></i>إحصائيات قاعدة البيانات
                            </h5>
                            <div class="row">
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h3>{{ table_counts.get('branches', 0) }}</h3>
                                            <p class="mb-0">الفروع</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h3>{{ table_counts.get('clinics', 0) }}</h3>
                                            <p class="mb-0">العيادات</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h3>{{ table_counts.get('drugs', 0) }}</h3>
                                            <p class="mb-0">الأدوية</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h3>{{ table_counts.get('dispensed', 0) }}</h3>
                                            <p class="mb-0">سجلات صرف الأدوية</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <div class="card bg-danger text-white">
                                        <div class="card-body text-center">
                                            <h3>{{ table_counts.get('insulin_dispensed', 0) }}</h3>
                                            <p class="mb-0">سجلات صرف الأنسولين</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- النسخ الاحتياطية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-success mb-3">
                                <i class="mdi mdi-backup-restore me-2"></i>النسخ الاحتياطية
                            </h5>
                            
                            <!-- إنشاء نسخة احتياطية -->
                            <div class="card border-success mb-3">
                                <div class="card-body">
                                    <h6 class="card-title text-success">
                                        <i class="mdi mdi-content-save me-2"></i>إنشاء نسخة احتياطية
                                    </h6>
                                    <p class="card-text text-muted">
                                        إنشاء نسخة احتياطية كاملة من قاعدة البيانات مع ضغط الملف
                                    </p>
                                    <form method="POST" action="/admin/create-backup" class="d-inline">
                                        <button type="submit" class="btn btn-success" onclick="return confirm('هل تريد إنشاء نسخة احتياطية جديدة؟')">
                                            <i class="mdi mdi-plus me-2"></i>إنشاء نسخة احتياطية
                                        </button>
                                        {% if backup_files %}
                                        <button type="button" class="btn btn-warning ms-2"
                                                onclick="showRestoreModal('{{ backup_files[0].name }}')">
                                            <i class="mdi mdi-restore me-2"></i>استعادة آخر نسخة
                                        </button>
                                        {% endif %}
                                    </form>
                                </div>
                            </div>

                            <!-- قائمة النسخ الاحتياطية -->
                            {% if backup_files %}
                            <div class="card border-info">
                                <div class="card-header bg-light">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="mdi mdi-file-multiple me-2"></i>النسخ الاحتياطية المتوفرة ({{ backup_files|length }})
                                        </h6>
                                        <div>
                                            <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="selectAllBackups()">
                                                <i class="mdi mdi-check-all me-1"></i>تحديد الكل
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="clearAllBackups()">
                                                <i class="mdi mdi-close-circle me-1"></i>إلغاء التحديد
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteSelectedBackups()">
                                                <i class="mdi mdi-delete me-1"></i>حذف المحدد
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <form id="deleteMultipleBackupsForm" method="POST" action="/admin/delete-multiple-backups">
                                        <div class="table-responsive">
                                            <table class="table table-hover mb-0">
                                                <thead class="bg-light">
                                                    <tr>
                                                        <th width="50">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="selectAllBackupsCheckbox" onchange="toggleAllBackups()">
                                                            </div>
                                                        </th>
                                                        <th>اسم الملف</th>
                                                        <th>الحجم</th>
                                                        <th>التاريخ</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                            <tbody>
                                                {% for backup in backup_files %}
                                                <tr>
                                                    <td>
                                                        <div class="form-check">
                                                            <input class="form-check-input backup-checkbox" type="checkbox"
                                                                   name="selected_backups" value="{{ backup.name }}"
                                                                   id="backup_{{ loop.index }}">
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <label for="backup_{{ loop.index }}" class="cursor-pointer">
                                                            <i class="mdi mdi-zip-box me-2 text-primary"></i>
                                                            {{ backup.name }}
                                                        </label>
                                                    </td>
                                                    <td>{{ backup.size }} KB</td>
                                                    <td>{{ backup.date }}</td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="/admin/download-backup/{{ backup.name }}"
                                                               class="btn btn-sm btn-primary" title="تحميل">
                                                                <i class="mdi mdi-download"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-sm btn-warning"
                                                                    onclick="showRestoreModal('{{ backup.name }}')" title="استعادة">
                                                                <i class="mdi mdi-restore"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-danger"
                                                                    onclick="if(confirm('هل تريد حذف هذه النسخة؟')) {
                                                                        var form = document.createElement('form');
                                                                        form.method = 'POST';
                                                                        form.action = '/admin/delete-backup/{{ backup.name }}';
                                                                        document.body.appendChild(form);
                                                                        form.submit();
                                                                    }" title="حذف">
                                                                <i class="mdi mdi-delete"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                        </div>
                                    </form>
                                </div>
                                <div class="card-footer bg-light">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="text-muted">عدد النسخ المحددة: <span id="selectedBackupsCount">0</span></span>
                                        </div>
                                        <button type="button" class="btn btn-danger" onclick="deleteSelectedBackups()">
                                            <i class="mdi mdi-delete me-1"></i>حذف النسخ المحددة
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="mdi mdi-information me-2"></i>
                                لا توجد نسخ احتياطية متوفرة حالياً
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <hr>

                    <!-- تفريغ البيانات -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="text-danger mb-3">
                                <i class="mdi mdi-delete-sweep me-2"></i>تفريغ البيانات
                            </h5>
                            
                            <div class="alert alert-warning">
                                <i class="mdi mdi-alert-triangle me-2"></i>
                                <strong>تحذير:</strong> عمليات التفريغ لا يمكن التراجع عنها. سيتم إنشاء نسخة احتياطية تلقائياً قبل التفريغ.
                            </div>

                            <!-- خيارات التفريغ -->
                            <div class="row">
                                <!-- تفريغ بيانات الصرف فقط -->
                                <div class="col-md-6 mb-3">
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <h6 class="card-title text-warning">
                                                <i class="mdi mdi-broom me-2"></i>تفريغ جزئي
                                            </h6>
                                            <p class="card-text small">
                                                حذف سجلات الصرف فقط (الأدوية والأنسولين) والاحتفاظ بالبيانات الأساسية (الفروع، العيادات، تصنيفات الأدوية)
                                            </p>
                                            <button type="button" class="btn btn-warning btn-sm"
                                                    onclick="if(confirm('هل تريد تفريغ بيانات الصرف فقط؟\n(سيتم حذف سجلات الأدوية والأنسولين)\nسيتم إنشاء نسخة احتياطية تلقائياً.')) {
                                                        var form = document.createElement('form');
                                                        form.method = 'POST';
                                                        form.action = '/admin/clear-data';
                                                        var input1 = document.createElement('input');
                                                        input1.type = 'hidden';
                                                        input1.name = 'clear_type';
                                                        input1.value = 'dispensing_only';
                                                        var input2 = document.createElement('input');
                                                        input2.type = 'hidden';
                                                        input2.name = 'confirm_text';
                                                        input2.value = 'تأكيد الحذف';
                                                        form.appendChild(input1);
                                                        form.appendChild(input2);
                                                        document.body.appendChild(form);
                                                        form.submit();
                                                    }">
                                                <i class="mdi mdi-delete-outline me-1"></i>تفريغ جزئي
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- تفريغ كامل -->
                                <div class="col-md-4 mb-3">
                                    <div class="card border-danger">
                                        <div class="card-body">
                                            <h6 class="card-title text-danger">
                                                <i class="mdi mdi-delete-forever me-2"></i>تفريغ كامل
                                            </h6>
                                            <p class="card-text small">
                                                حذف جميع البيانات من قاعدة البيانات (الفروع، العيادات، الأدوية، سجلات الصرف)
                                            </p>
                                            <button type="button" class="btn btn-danger btn-sm"
                                                    onclick="if(confirm('⚠️ تحذير: هذا سيحذف جميع البيانات!\nهل أنت متأكد؟\nسيتم إنشاء نسخة احتياطية تلقائياً.')) {
                                                        var form = document.createElement('form');
                                                        form.method = 'POST';
                                                        form.action = '/admin/clear-data';
                                                        var input1 = document.createElement('input');
                                                        input1.type = 'hidden';
                                                        input1.name = 'clear_type';
                                                        input1.value = 'all_data';
                                                        var input2 = document.createElement('input');
                                                        input2.type = 'hidden';
                                                        input2.name = 'confirm_text';
                                                        input2.value = 'تأكيد الحذف';
                                                        form.appendChild(input1);
                                                        form.appendChild(input2);
                                                        document.body.appendChild(form);
                                                        form.submit();
                                                    }">
                                                <i class="mdi mdi-delete-forever me-1"></i>تفريغ كامل
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- تفريغ متقدم -->
                                <div class="col-md-4 mb-3">
                                    <div class="card border-info">
                                        <div class="card-body">
                                            <h6 class="card-title text-info">
                                                <i class="mdi mdi-tune-vertical me-2"></i>تفريغ متقدم
                                            </h6>
                                            <p class="card-text small">
                                                تفريغ محتويات محددة: تصنيفات، عيادات، فترات زمنية
                                            </p>
                                            <a href="/admin/advanced-clear" class="btn btn-info btn-sm">
                                                <i class="mdi mdi-settings-outline me-1"></i>تفريغ متقدم
                                            </a>
                                            <br><br>
                                            <small class="text-muted">
                                                التصنيفات: {{ categories|length if categories else 0 }} |
                                                العيادات: {{ clinics|length if clinics else 0 }}
                                            </small>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تفريغ متقدم -->
<div class="modal fade" id="advancedClearModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="mdi mdi-tune-vertical me-2"></i>تفريغ متقدم
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="mdi mdi-alert-triangle me-2"></i>
                    اختر نوع التفريغ والعناصر المحددة. سيتم إنشاء نسخة احتياطية تلقائياً قبل التفريغ.
                </div>

                <!-- تبويبات التفريغ المتقدم -->
                <ul class="nav nav-tabs" id="advancedClearTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="categories-tab" data-bs-toggle="tab"
                                data-bs-target="#categories-pane" type="button" role="tab">
                            <i class="mdi mdi-tag-multiple me-2"></i>حسب التصنيف
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="insulin-types-tab" data-bs-toggle="tab"
                                data-bs-target="#insulin-types-pane" type="button" role="tab">
                            <i class="mdi mdi-needle me-2"></i>حسب نوع الأنسولين
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="clinics-tab" data-bs-toggle="tab"
                                data-bs-target="#clinics-pane" type="button" role="tab">
                            <i class="mdi mdi-hospital-building me-2"></i>حسب العيادة
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="date-tab" data-bs-toggle="tab"
                                data-bs-target="#date-pane" type="button" role="tab">
                            <i class="mdi mdi-calendar-range me-2"></i>حسب الفترة الزمنية
                        </button>
                    </li>
                </ul>

                <div class="tab-content mt-3" id="advancedClearTabsContent">
                    <!-- تفريغ حسب التصنيف -->
                    <div class="tab-pane fade show active" id="categories-pane" role="tabpanel">
                        <form id="categoriesClearForm" method="POST" action="/admin/clear-data">
                            <input type="hidden" name="clear_type" value="advanced">
                            <input type="hidden" name="advanced_type" value="categories">
                            <input type="hidden" name="confirm_text" value="تأكيد الحذف">

                            <h6 class="text-primary mb-3">اختر التصنيفات المراد تفريغ بياناتها:</h6>



                            {% if categories and categories|length > 0 %}
                            <div class="row">
                                {% for category in categories %}
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               name="selected_categories" value="{{ category.id }}"
                                               id="cat_{{ category.id }}">
                                        <label class="form-check-label" for="cat_{{ category.id }}">
                                            <strong>{{ category.name }}</strong>
                                            <br><small class="text-muted">
                                                {{ category.drugs_count }} دواء،
                                                {{ category.dispensed_count }} سجل صرف
                                            </small>
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="alert alert-warning">
                                <i class="mdi mdi-alert-triangle me-2"></i>
                                لا توجد تصنيفات متوفرة حالياً
                            </div>
                            {% endif %}

                            <div class="mt-3">
                                <button type="button" class="btn btn-secondary btn-sm me-2" onclick="selectAllCategories()">
                                    تحديد الكل
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm" onclick="clearAllCategories()">
                                    إلغاء التحديد
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- تفريغ حسب نوع الأنسولين -->
                    <div class="tab-pane fade" id="insulin-types-pane" role="tabpanel">
                        <form id="insulinTypesClearForm" method="POST" action="/admin/clear-data">
                            <input type="hidden" name="clear_type" value="advanced">
                            <input type="hidden" name="advanced_type" value="insulin_types">
                            <input type="hidden" name="confirm_text" value="تأكيد الحذف">

                            <h6 class="text-primary mb-3">اختر أنواع الأنسولين المراد تفريغ بياناتها:</h6>

                            {% if insulin_types and insulin_types|length > 0 %}
                            <div class="row">
                                {% for type in insulin_types %}
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               name="selected_insulin_types" value="{{ type.name }}"
                                               id="insulin_type_{{ type.id }}">
                                        <label class="form-check-label" for="insulin_type_{{ type.id }}">
                                            <strong>{{ type.name }}</strong>
                                            <br><small class="text-muted">{{ type.insulin_count }} سجل أنسولين</small>
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="alert alert-warning">
                                <i class="mdi mdi-alert-triangle me-2"></i>
                                لا توجد أنواع أنسولين متوفرة حالياً
                            </div>
                            {% endif %}

                            <div class="mt-3">
                                <button type="button" class="btn btn-secondary btn-sm me-2" onclick="selectAllInsulinTypes()">
                                    تحديد الكل
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm" onclick="clearAllInsulinTypes()">
                                    إلغاء التحديد
                                </button>
                            </div>

                            {% if insulin_types and insulin_types|length > 0 %}
                            <div class="mt-3">
                                <button type="submit" class="btn btn-danger"
                                        onclick="return confirm('⚠️ تحذير: سيتم حذف جميع سجلات الأنسولين للأنواع المحددة!\nهل أنت متأكد؟')">
                                    <i class="mdi mdi-delete me-1"></i>تفريغ الأنواع المحددة
                                </button>
                            </div>
                            {% endif %}
                        </form>
                    </div>

                    <!-- تفريغ حسب العيادة -->
                    <div class="tab-pane fade" id="clinics-pane" role="tabpanel">
                        <form id="clinicsClearForm" method="POST" action="/admin/clear-data">
                            <input type="hidden" name="clear_type" value="advanced">
                            <input type="hidden" name="advanced_type" value="clinics">
                            <input type="hidden" name="confirm_text" value="تأكيد الحذف">

                            <h6 class="text-primary mb-3">اختر العيادات المراد تفريغ بياناتها:</h6>



                            {% if clinics and clinics|length > 0 %}
                            <div class="row">
                                {% for clinic in clinics %}
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               name="selected_clinics" value="{{ clinic.id }}"
                                               id="clinic_{{ clinic.id }}">
                                        <label class="form-check-label" for="clinic_{{ clinic.id }}">
                                            <strong>{{ clinic.name }}</strong>
                                            <br><small class="text-muted">
                                                {{ clinic.area_name }} - {{ clinic.branch_name }}
                                                <br>{{ clinic.dispensed_count }} سجل أدوية،
                                                {{ clinic.insulin_count }} سجل أنسولين
                                            </small>
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="alert alert-warning">
                                <i class="mdi mdi-alert-triangle me-2"></i>
                                لا توجد عيادات متوفرة حالياً
                            </div>
                            {% endif %}

                            <div class="mt-3">
                                <button type="button" class="btn btn-secondary btn-sm me-2" onclick="selectAllClinics()">
                                    تحديد الكل
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm" onclick="clearAllClinics()">
                                    إلغاء التحديد
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- تفريغ حسب الفترة الزمنية -->
                    <div class="tab-pane fade" id="date-pane" role="tabpanel">
                        <form id="dateClearForm" method="POST" action="/admin/clear-data">
                            <input type="hidden" name="clear_type" value="advanced">
                            <input type="hidden" name="advanced_type" value="date_range">
                            <input type="hidden" name="confirm_text" value="تأكيد الحذف">

                            <h6 class="text-primary mb-3">حدد الفترة الزمنية المراد تفريغ بياناتها:</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="start_date" class="form-label">تاريخ البداية:</label>
                                    <input type="month" class="form-control" name="start_date" id="start_date" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="end_date" class="form-label">تاريخ النهاية:</label>
                                    <input type="month" class="form-control" name="end_date" id="end_date" required>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="mdi mdi-information me-2"></i>
                                سيتم حذف جميع سجلات الصرف (أدوية وأنسولين) في الفترة المحددة
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" onclick="submitAdvancedClear()">
                    <i class="mdi mdi-delete me-2"></i>تفريغ المحدد
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function showAdvancedClearModal() {
    new bootstrap.Modal(document.getElementById('advancedClearModal')).show();
}

function selectAllCategories() {
    document.querySelectorAll('input[name="selected_categories"]').forEach(cb => cb.checked = true);
}

function clearAllCategories() {
    document.querySelectorAll('input[name="selected_categories"]').forEach(cb => cb.checked = false);
}

function selectAllInsulinTypes() {
    document.querySelectorAll('input[name="selected_insulin_types"]').forEach(cb => cb.checked = true);
}

function clearAllInsulinTypes() {
    document.querySelectorAll('input[name="selected_insulin_types"]').forEach(cb => cb.checked = false);
}

function selectAllClinics() {
    document.querySelectorAll('input[name="selected_clinics"]').forEach(cb => cb.checked = true);
}

function clearAllClinics() {
    document.querySelectorAll('input[name="selected_clinics"]').forEach(cb => cb.checked = false);
}

function submitAdvancedClear() {
    var activeTab = document.querySelector('.nav-link.active').id;
    var form;
    var message;

    if (activeTab === 'categories-tab') {
        form = document.getElementById('categoriesClearForm');
        var selectedCategories = document.querySelectorAll('input[name="selected_categories"]:checked');
        if (selectedCategories.length === 0) {
            alert('يجب اختيار تصنيف واحد على الأقل');
            return;
        }
        message = `هل تريد تفريغ بيانات ${selectedCategories.length} تصنيف محدد؟\nسيتم حذف جميع سجلات الصرف لهذه التصنيفات.`;
    } else if (activeTab === 'insulin-types-tab') {
        form = document.getElementById('insulinTypesClearForm');
        var selectedInsulinTypes = document.querySelectorAll('input[name="selected_insulin_types"]:checked');
        if (selectedInsulinTypes.length === 0) {
            alert('يجب اختيار نوع أنسولين واحد على الأقل');
            return;
        }
        message = `هل تريد تفريغ بيانات ${selectedInsulinTypes.length} نوع أنسولين محدد؟\nسيتم حذف جميع سجلات الأنسولين لهذه الأنواع.`;
    } else if (activeTab === 'clinics-tab') {
        form = document.getElementById('clinicsClearForm');
        var selectedClinics = document.querySelectorAll('input[name="selected_clinics"]:checked');
        if (selectedClinics.length === 0) {
            alert('يجب اختيار عيادة واحدة على الأقل');
            return;
        }
        message = `هل تريد تفريغ بيانات ${selectedClinics.length} عيادة محددة؟\nسيتم حذف جميع سجلات الصرف لهذه العيادات.`;
    } else if (activeTab === 'date-tab') {
        form = document.getElementById('dateClearForm');
        var startDate = document.getElementById('start_date').value;
        var endDate = document.getElementById('end_date').value;
        if (!startDate || !endDate) {
            alert('يجب تحديد تاريخ البداية والنهاية');
            return;
        }
        message = `هل تريد تفريغ بيانات الفترة من ${startDate} إلى ${endDate}؟\nسيتم حذف جميع سجلات الصرف في هذه الفترة.`;
    }

    if (confirm(message + '\n\nسيتم إنشاء نسخة احتياطية تلقائياً قبل التفريغ.')) {
        form.submit();
    }
}

function showRestoreModal(backupFilename) {
    document.getElementById('restoreBackupFilename').value = backupFilename;
    document.getElementById('restoreBackupName').textContent = backupFilename;
    new bootstrap.Modal(document.getElementById('restoreBackupModal')).show();
}

// دوال التحكم في النسخ الاحتياطية
function toggleAllBackups() {
    const mainCheckbox = document.getElementById('selectAllBackupsCheckbox');
    const checkboxes = document.querySelectorAll('.backup-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = mainCheckbox.checked;
    });

    updateSelectedCount();
}

function selectAllBackups() {
    const mainCheckbox = document.getElementById('selectAllBackupsCheckbox');
    mainCheckbox.checked = true;
    toggleAllBackups();
}

function clearAllBackups() {
    const mainCheckbox = document.getElementById('selectAllBackupsCheckbox');
    mainCheckbox.checked = false;
    toggleAllBackups();
}

function updateSelectedCount() {
    const selectedCount = document.querySelectorAll('.backup-checkbox:checked').length;
    document.getElementById('selectedBackupsCount').textContent = selectedCount;
}

function deleteSelectedBackups() {
    const selectedBackups = document.querySelectorAll('.backup-checkbox:checked');
    if (selectedBackups.length === 0) {
        alert('يرجى تحديد نسخة احتياطية واحدة على الأقل للحذف');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف ${selectedBackups.length} نسخة احتياطية؟\n\n⚠️ تحذير: هذه العملية لا يمكن التراجع عنها!`)) {
        document.getElementById('deleteMultipleBackupsForm').submit();
    }
}

// إضافة مستمع للتحديث التلقائي لعدد العناصر المحددة
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.backup-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
});
</script>

<!-- نافذة منبثقة لاستعادة النسخة الاحتياطية -->
<div class="modal fade" id="restoreBackupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="mdi mdi-restore me-2"></i>استعادة نسخة احتياطية
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="mdi mdi-alert-triangle me-2"></i>
                    <strong>تحذير مهم:</strong> ستؤدي هذه العملية إلى:
                    <ul class="mt-2 mb-0">
                        <li>حذف جميع البيانات الحالية</li>
                        <li>استبدالها ببيانات النسخة الاحتياطية</li>
                        <li>إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة</li>
                    </ul>
                </div>

                <p><strong>النسخة المحددة:</strong> <span id="restoreBackupName"></span></p>

                <form id="restoreBackupForm" method="POST" action="/admin/restore-backup">
                    <input type="hidden" id="restoreBackupFilename" name="backup_filename">

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmRestore" name="confirm_restore" value="confirmed" required>
                            <label class="form-check-label fw-bold text-danger" for="confirmRestore">
                                ✅ أؤكد أنني أريد استعادة هذه النسخة وحذف جميع البيانات الحالية
                            </label>
                        </div>
                        <small class="text-muted">
                            <i class="mdi mdi-information me-1"></i>
                            سيتم إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="mdi mdi-close me-1"></i>إلغاء
                </button>
                <button type="button" class="btn btn-warning" onclick="submitRestoreForm()">
                    <i class="mdi mdi-restore me-2"></i>استعادة النسخة
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function submitRestoreForm() {
    const confirmCheckbox = document.getElementById('confirmRestore');
    if (!confirmCheckbox.checked) {
        alert('يجب تأكيد الاستعادة أولاً بوضع علامة ✅');
        return;
    }

    const backupName = document.getElementById('restoreBackupName').textContent;
    if (confirm(`هل أنت متأكد من استعادة النسخة: ${backupName}؟\n\n⚠️ سيتم حذف جميع البيانات الحالية!\n✅ سيتم إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة.`)) {
        document.getElementById('restoreBackupForm').submit();
    }
}
</script>

{% endblock %}
