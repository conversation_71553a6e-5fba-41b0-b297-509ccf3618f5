{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block head %}
<style>
@media print {
    /* إخفاء الأزرار وخيارات التصفية عند الطباعة */
    .btn, .card-header .d-flex .d-flex, .no-print {
        display: none !important;
    }

    /* تحسين عرض الطباعة */
    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .card-header {
        background: #343a40 !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    /* تحسين عرض الجداول */
    table {
        font-size: 12px !important;
    }

    thead {
        background: #343a40 !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    /* تحسين عرض بطاقات الإحصائيات للطباعة */
    .bg-primary, .bg-success, .bg-warning, .bg-info {
        background: white !important;
        color: black !important;
        border: 1px solid #333 !important;
    }

    /* إخفاء بطاقات الإحصائيات الملونة */
    .row.mb-4:has(.bg-primary) {
        display: none !important;
    }

    /* تحسين عرض الصفحة */
    body {
        font-size: 12px !important;
    }

    .container-fluid {
        padding: 0 !important;
    }

    .report-logo {
        max-width: 60px !important;
        height: auto !important;
        margin: 0 auto 8px !important;
        display: block !important;
    }
}

.report-logo {
    max-width: 80px;
    height: auto;
    margin: 0 auto 8px;
    display: block;
}

.table-dark-header {
    background-color: #343a40 !important;
    color: white !important;
}

.table-dark-footer {
    background-color: #6c757d !important;
    color: white !important;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- عنوان التقرير -->
            <div class="card border-primary mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="mdi mdi-needle me-2"></i>{{ title }}
                        </h4>
                        <div class="d-flex gap-2 no-print">
                            <button type="button" onclick="window.print()" class="btn btn-light btn-sm">
                                <i class="mdi mdi-printer me-1"></i>طباعة
                            </button>
                            <a href="{{ url_for('export_insulin_grouped_excel', **request.args) }}" class="btn btn-success btn-sm">
                                <i class="mdi mdi-file-excel me-1"></i>تصدير Excel
                            </a>
                            <a href="{{ url_for('export_insulin_grouped_pdf', **request.args) }}" class="btn btn-danger btn-sm">
                                <i class="mdi mdi-file-pdf me-1"></i>تصدير PDF
                            </a>
                            <a href="{{ url_for('reports') }}" class="btn btn-light btn-sm">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- الشعار -->
                    <div class="text-center">
                        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo">
                    </div>

                    <!-- معلومات التقرير -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>الفترة الزمنية:</strong> {{ date_range_text }}
                        </div>
                        <div class="col-md-4">
                            <strong>تاريخ التقرير:</strong> {{ current_date }}
                        </div>
                        <div class="col-md-4">
                            <strong>عدد المجموعات:</strong> {{ grouped_data|length }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- الإحصائيات الإجمالية -->
            <div class="row mb-4 no-print">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3>{{ total_cases }}</h3>
                            <p class="mb-0">إجمالي الحالات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3>{{ total_quantity }}</h3>
                            <p class="mb-0">إجمالي الكمية</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3>{{ "%.2f"|format(total_cost) }}</h3>
                            <p class="mb-0">إجمالي التكلفة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>{{ grouped_data|length }}</h3>
                            <p class="mb-0">عدد المجموعات</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- عرض البيانات المجمعة -->
            {% if grouped_data %}
                {% for group_name, group_info in grouped_data.items() %}
                <div class="card mb-4">
                    <div class="card-header {% if group_info.type == 'مدعم' %}bg-success{% else %}bg-primary{% endif %} text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="mdi mdi-needle me-2"></i>
                                {{ group_name }}
                            </h5>
                            <div class="d-flex gap-3">
                                <span class="badge bg-light text-dark">
                                    <i class="mdi mdi-account-group me-1"></i>{{ group_info.cases }} حالة
                                </span>
                                <span class="badge bg-light text-dark">
                                    <i class="mdi mdi-package-variant me-1"></i>{{ group_info.quantity }} وحدة
                                </span>
                                <span class="badge bg-light text-dark">
                                    <i class="mdi mdi-currency-usd me-1"></i>{{ "%.2f"|format(group_info.cost) }} ج.م
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- خلية ثابتة توضح نوع الاختيار -->
                        <div class="alert alert-info mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>نوع الاختيار:</strong> فئة + نوع
                                </div>
                                <div class="col-md-6">
                                    <strong>المحدد:</strong> {{ group_info.category }} - {{ group_info.type }}
                                </div>
                            </div>
                        </div>

                        {% if group_info.data %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark-header">
                                    <tr>
                                        <th>#</th>
                                        <th>الصنف</th>
                                        <th>النوع</th>
                                        <th>الفئة</th>
                                        <th>الوحدة</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>المعدل</th>
                                        <th>الرصيد</th>
                                        <th>عدد الحالات</th>
                                        <th>التكلفة</th>
                                        {% if show_location %}
                                        <th>{{ location_type }}</th>
                                        {% endif %}
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in group_info.data %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ item.name }}</td>
                                        <td>
                                            <span class="badge {% if item.type == 'مدعم' %}bg-success{% else %}bg-primary{% endif %}">
                                                {{ item.type }}
                                            </span>
                                        </td>
                                        <td>{{ item.category }}</td>
                                        <td>{{ item.unit }}</td>
                                        <td>{{ "%.2f"|format(item.price) }}</td>
                                        <td>{{ item.quantity }}</td>
                                        <td>{{ item.rate or '-' }}</td>
                                        <td>{{ item.id_number or '-' }}</td>
                                        <td>{{ item.cases_count }}</td>
                                        <td>{{ "%.2f"|format(item.cost) }}</td>
                                        {% if show_location %}
                                        <td>
                                            {% if location_type == 'العيادة' %}
                                                {{ item.clinic_name }}
                                            {% elif location_type == 'المنطقة' %}
                                                {{ item.area_name }}
                                            {% elif location_type == 'الفرع' %}
                                                {{ item.branch_name }}
                                            {% endif %}
                                        </td>
                                        {% endif %}
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot class="table-dark-footer">
                                    <tr>
                                        <th colspan="{% if show_location %}8{% else %}7{% endif %}">إجمالي المجموعة:</th>
                                        <th>-</th>
                                        <th>{{ group_info.cases }}</th>
                                        <th>{{ "%.2f"|format(group_info.cost) }}</th>
                                        {% if show_location %}
                                        <th>-</th>
                                        {% endif %}
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-warning text-center">
                            <i class="mdi mdi-information-outline me-2"></i>
                            لا توجد بيانات لهذه المجموعة
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            {% else %}
            <div class="card">
                <div class="card-body">
                    <div class="alert alert-info text-center">
                        <i class="mdi mdi-information-outline me-2"></i>
                        لا توجد بيانات أنسولين متاحة للعرض في الفترة المحددة
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- ملخص نهائي -->
            {% if grouped_data %}
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="mdi mdi-chart-pie me-2"></i>الملخص النهائي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <h4 class="text-primary">{{ grouped_data|length }}</h4>
                            <p class="mb-0">مجموعة (فئة + نوع)</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-success">{{ total_cases }}</h4>
                            <p class="mb-0">إجمالي الحالات</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-warning">{{ total_quantity }}</h4>
                            <p class="mb-0">إجمالي الكمية</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-danger">{{ "%.2f"|format(total_cost) }}</h4>
                            <p class="mb-0">إجمالي التكلفة (ج.م)</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}
