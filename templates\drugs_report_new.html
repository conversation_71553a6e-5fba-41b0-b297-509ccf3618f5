{% extends 'base.html' %}

{% block title %}تقرير الأدوية: {{ category_name }}{% endblock %}

{% block styles %}
<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .report-logo {
        max-width: 150px;
        height: auto;
        margin: 0 auto 15px;
        display: block;
    }

    .info-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        text-align: center;
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }

    .info-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .info-card i {
        font-size: 1.5rem;
        margin-bottom: 8px;
        display: block;
    }

    .info-card h5 {
        color: #4e73df;
        margin-bottom: 0.5rem;
    }

    .info-card p {
        color: #5a5c69;
        margin-bottom: 0;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .drug-name {
        font-weight: bold;
        color: #495057;
    }

    .scientific-name {
        font-size: 0.9rem;
        color: #6c757d;
        font-style: italic;
    }

    .cost-highlight {
        font-weight: bold;
        color: #28a745;
        background-color: rgba(40, 167, 69, 0.1);
        padding: 4px 8px;
        border-radius: 4px;
    }

    .card {
        border-radius: 15px;
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .table {
        border-radius: 10px;
        overflow: hidden;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.1);
        transform: scale(1.01);
    }


</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- أزرار الطباعة والتصدير في الأعلى -->
    <div class="row mb-3 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="exportToExcel()" title="تصدير إلى Excel">
                        <i class="mdi mdi-file-excel me-1"></i>تصدير Excel
                    </button>
                    <button class="btn btn-info" onclick="showPrintOptions('pdf')" title="حفظ كـ PDF">
                        <i class="mdi mdi-file-pdf me-1"></i>حفظ PDF
                    </button>
                    <button class="btn btn-primary" onclick="showPrintOptions('print')" title="طباعة التقرير">
                        <i class="mdi mdi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="mdi mdi-pill me-2"></i>تقرير الأدوية: {{ category_name }}
                </h4>
                <div>
                    <span class="badge bg-light text-dark">
                        <i class="mdi mdi-calendar-range me-1"></i>{{ date_range_text }}
                    </span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo">
                <h3 class="mt-3">تقرير استهلاك الأدوية</h3>
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="info-card">
                            <i class="mdi mdi-tag-multiple text-primary"></i>
                            <strong>التصنيف:</strong> {{ category_name }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-card">
                            <i class="mdi mdi-map-marker text-success"></i>
                            <strong>النطاق:</strong> {{ scope_name }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-card">
                            <i class="mdi mdi-calendar-range text-info"></i>
                            <strong>الفترة:</strong> {{ date_range_text }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- بطاقات الإحصائيات -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                        <div class="card-body text-white text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="me-3">
                                    <i class="mdi mdi-pill" style="font-size: 2.5rem;"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0">{{ unique_drugs }}</h3>
                                    <p class="mb-0">دواء مختلف</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                        <div class="card-body text-white text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="me-3">
                                    <i class="mdi mdi-package-variant" style="font-size: 2.5rem;"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0">{{ total_quantity|round(0) }}</h3>
                                    <p class="mb-0">إجمالي الكمية</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="me-3">
                                    <i class="mdi mdi-account-group text-primary" style="font-size: 2.5rem;"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0 text-dark">{{ total_cases }}</h3>
                                    <p class="mb-0 text-muted">إجمالي الحالات</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <div class="card-body text-white text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="me-3">
                                    <i class="mdi mdi-cash-multiple" style="font-size: 2.5rem;"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0">{{ total_cost|round(2) }}</h4>
                                    <p class="mb-0">جنيه</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {% if drugs_data %}
                <div class="card mb-4 shadow-sm border-0">
                    <div class="card-header" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white; border-radius: 15px 15px 0 0;">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="mdi mdi-pill me-2"></i>تفاصيل الأدوية المنصرفة</h5>
                            <div>
                                <span class="badge bg-light text-dark me-2">
                                    <i class="mdi mdi-account-group me-1"></i>{{ total_cases }} حالة
                                </span>
                                <span class="badge bg-warning text-dark">
                                    <i class="mdi mdi-cash me-1"></i>{{ total_cost|round(2) }} جنيه
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover mb-0">
                                <thead style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                    <tr>
                                        <th class="text-center" style="border: none; padding: 15px; font-weight: bold; min-width: 50px;">
                                            <i class="mdi mdi-numeric me-1" style="color: #f1c40f;"></i>#
                                        </th>
                                        <th style="border: none; padding: 15px; font-weight: bold; min-width: 200px;">
                                            <i class="mdi mdi-pill me-1" style="color: #f39c12;"></i>اسم الدواء
                                        </th>
                                        <th style="border: none; padding: 15px; font-weight: bold; min-width: 150px;">
                                            <i class="mdi mdi-flask me-1" style="color: #3498db;"></i>الاسم العلمي
                                        </th>
                                        <th style="border: none; padding: 15px; font-weight: bold; min-width: 120px;">
                                            <i class="mdi mdi-tag me-1" style="color: #9b59b6;"></i>التصنيف
                                        </th>
                                        <th class="text-center" style="border: none; padding: 15px; font-weight: bold; min-width: 100px;">
                                            <i class="mdi mdi-package-variant me-1" style="color: #2ecc71;"></i>الكمية
                                        </th>
                                        <th class="text-center" style="border: none; padding: 15px; font-weight: bold; min-width: 100px;">
                                            <i class="mdi mdi-currency-usd me-1" style="color: #f1c40f;"></i>السعر
                                        </th>
                                        <th class="text-center" style="border: none; padding: 15px; font-weight: bold; min-width: 120px;">
                                            <i class="mdi mdi-account-group me-1" style="color: #e74c3c;"></i>عدد الحالات
                                        </th>
                                        <th class="text-center" style="border: none; padding: 15px; font-weight: bold; min-width: 120px;">
                                            <i class="mdi mdi-cash-multiple me-1" style="color: #f1c40f;"></i>التكلفة الإجمالية
                                        </th>
                                        <th style="border: none; padding: 15px; font-weight: bold; min-width: 150px;">
                                            <i class="mdi mdi-hospital-building me-1" style="color: #17a2b8;"></i>العيادة
                                        </th>
                                        <th style="border: none; padding: 15px; font-weight: bold; min-width: 120px;">
                                            <i class="mdi mdi-map-marker me-1" style="color: #fd7e14;"></i>المنطقة
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for drug in drugs_data %}
                                    <tr>
                                        <td class="text-center">{{ loop.index }}</td>
                                        <td class="drug-name">{{ drug.drug_name }}</td>
                                        <td class="scientific-name">{{ drug.scientific_name or 'غير محدد' }}</td>
                                        <td>{{ drug.category_name }}</td>
                                        <td class="text-center">{{ drug.total_quantity }}</td>
                                        <td class="text-center">{{ drug.price|round(2) }}</td>
                                        <td class="text-center">{{ drug.total_cases }}</td>
                                        <td class="text-center cost-highlight">{{ drug.total_cost|round(2) }}</td>
                                        <td>{{ drug.clinic_name }}</td>
                                        <td>{{ drug.area_name }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                    <tr>
                                        <th colspan="6" class="text-start" style="border: none; font-weight: bold; padding: 15px;">
                                            <i class="mdi mdi-sigma me-1"></i>الإجمالي العام
                                        </th>
                                        <th class="text-center" style="border: none; font-weight: bold; color: #495057; padding: 15px;">
                                            {{ total_cases }}
                                        </th>
                                        <th class="text-center" style="border: none; font-weight: bold; color: #28a745; padding: 15px;">
                                            {{ total_cost|round(2) }} جنيه
                                        </th>
                                        <th colspan="2" class="text-center" style="border: none; font-weight: bold; color: #6c757d; padding: 15px;">
                                            {{ unique_drugs }} دواء مختلف
                                        </th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            {% else %}
            <div class="alert alert-info">
                <i class="mdi mdi-information-outline me-2"></i>لا توجد بيانات منصرفة في الفترة المحددة
            </div>
            {% endif %}

            <!-- ملخص التكلفة النهائي -->
            <div class="card shadow-lg border-0 mt-4">
                <div class="card-header text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0;">
                    <h4 class="mb-0"><i class="mdi mdi-chart-pie me-2"></i>الملخص النهائي للتقرير</h4>
                </div>
                <div class="card-body" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                                <div class="card-body text-white text-center">
                                    <i class="mdi mdi-pill mb-3" style="font-size: 3rem;"></i>
                                    <h2 class="mb-2">{{ unique_drugs }}</h2>
                                    <h5 class="mb-0">دواء مختلف</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                                <div class="card-body text-white text-center">
                                    <i class="mdi mdi-package-variant mb-3" style="font-size: 3rem;"></i>
                                    <h2 class="mb-2">{{ total_quantity|round(0) }}</h2>
                                    <h5 class="mb-0">إجمالي الكمية</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                                <div class="card-body text-center">
                                    <i class="mdi mdi-account-group mb-3 text-primary" style="font-size: 3rem;"></i>
                                    <h2 class="mb-2 text-dark">{{ total_cases }}</h2>
                                    <h5 class="mb-0 text-dark">إجمالي الحالات</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <div class="card-body text-white text-center">
                                    <i class="mdi mdi-cash-multiple mb-3" style="font-size: 3rem;"></i>
                                    <h2 class="mb-2">{{ total_cost|round(2) }}</h2>
                                    <h5 class="mb-0">إجمالي التكلفة (جنيه)</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- مربع حوار خيارات الطباعة -->
    <div id="printOptionsModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">خيارات الطباعة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label class="form-label">اتجاه الصفحة:</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="pageOrientation" id="orientationLandscape" value="landscape" checked>
                            <label class="form-check-label" for="orientationLandscape">
                                أفقي (Landscape)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="pageOrientation" id="orientationPortrait" value="portrait">
                            <label class="form-check-label" for="orientationPortrait">
                                رأسي (Portrait)
                            </label>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">حجم الخط:</label>
                        <select class="form-select" id="fontSize">
                            <option value="small">صغير</option>
                            <option value="medium" selected>متوسط</option>
                            <option value="large">كبير</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="confirmPrint">طباعة</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}



{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script>
    // متغيرات عامة
    var printAction = 'print'; // 'print' أو 'pdf'
    var printModal;

    // التأكد من تحميل الصفحة بشكل كامل
    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل الصفحة بنجاح');

        // التأكد من وجود زر PDF
        var pdfButton = document.getElementById('pdf-button');
        if (pdfButton) {
            console.log('تم العثور على زر PDF');

            // إضافة حدث النقر على زر PDF
            pdfButton.addEventListener('click', function() {
                console.log('تم النقر على زر PDF');
                exportToPDF();
            });
        } else {
            console.error('لم يتم العثور على زر PDF');
        }
    });

    // وظيفة إظهار خيارات الطباعة
    function showPrintOptions(action) {
        if (action === 'pdf') {
            exportToPDF();
            return;
        }

        printAction = action;

        // تهيئة مربع الحوار إذا لم يكن موجودًا
        if (!printModal) {
            printModal = new bootstrap.Modal(document.getElementById('printOptionsModal'));

            // إضافة حدث للزر "طباعة"
            document.getElementById('confirmPrint').addEventListener('click', function() {
                printModal.hide();
                performPrint();
            });
        }

        // عرض مربع الحوار
        printModal.show();
    }

    // وظيفة تنفيذ الطباعة بناءً على الخيارات المحددة
    function performPrint() {
        var elementsToHide, container, originalOverflow, originalWidth, styleElement;

        try {
            // الحصول على الخيارات المحددة
            var orientation = document.querySelector('input[name="pageOrientation"]:checked').value;
            var fontSize = document.getElementById('fontSize').value;

            // إخفاء العناصر غير المطلوبة للطباعة
            elementsToHide = document.querySelectorAll('.no-print, .btn, button, .row.mb-3, .d-flex.justify-content-between, .badge, .action-buttons');
            elementsToHide.forEach(function(element) {
                element.style.display = 'none';
            });

            // إزالة المسافات الإضافية من البداية
            container = document.querySelector('.container-fluid');
            if (container) {
                container.style.paddingTop = '0';
                container.style.marginTop = '0';
            }

            // تطبيق بعض التعديلات قبل الطباعة
            originalOverflow = document.body.style.overflow;
            originalWidth = document.body.style.width;

            // تعديل نمط الصفحة للطباعة
            document.body.style.overflow = 'visible';
            document.body.style.width = 'auto';

            // تطبيق اتجاه الصفحة
            styleElement = document.createElement('style');
            styleElement.id = 'print-orientation-style';
            styleElement.innerHTML = '@page { size: ' + orientation + '; margin: 0.1in; }';
            styleElement.innerHTML += '@media print { body { margin: 0; padding: 0; } }';

            // تطبيق حجم الخط
            var fontSizeMap = {
                'small': '7.5pt',
                'medium': '9pt',
                'large': '11pt'
            };

            // إضافة CSS شامل للطباعة
            styleElement.innerHTML += `
                @media print {
                    * {
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                    }

                    .no-print, .btn, button, .row.mb-3, .d-flex.justify-content-between, .badge, .action-buttons {
                        display: none !important;
                    }

                    .container-fluid {
                        padding: 0 !important;
                        margin: 0 !important;
                        max-width: 100% !important;
                        width: 100% !important;
                    }

                    .card {
                        border: none !important;
                        box-shadow: none !important;
                        margin: 0 !important;
                        page-break-inside: avoid;
                    }

                    .card-header {
                        background-color: #f8f9fa !important;
                        color: #000 !important;
                        border-bottom: 1px solid #333 !important;
                        padding: 4px !important;
                    }

                    .table {
                        width: 100% !important;
                        border-collapse: collapse !important;
                        font-size: ${fontSizeMap[fontSize]} !important;
                        margin: 0 !important;
                        table-layout: auto !important;
                    }

                    .table th, .table td {
                        border: 1px solid #333 !important;
                        padding: 1px !important;
                        text-align: center !important;
                        vertical-align: middle !important;
                        word-wrap: break-word !important;
                        line-height: 1.1 !important;
                    }

                    /* إخفاء عمود الاسم العلمي عند الطباعة */
                    .table th:nth-child(3), .table td:nth-child(3) {
                        display: none !important;
                    }

                    .table th {
                        background-color: #e9ecef !important;
                        font-weight: bold !important;
                        font-size: ${parseInt(fontSizeMap[fontSize]) - 1}pt !important;
                    }

                    .table td {
                        font-size: ${fontSizeMap[fontSize]} !important;
                    }

                    .info-card {
                        border: 1px solid #333 !important;
                        margin: 2px 0 !important;
                        padding: 3px !important;
                        background-color: #f9f9f9 !important;
                        font-size: 8pt !important;
                    }

                    .report-logo {
                        max-width: 75px !important;
                        height: auto !important;
                        margin: 0 auto 4px !important;
                        display: block !important;
                    }
                }
            `;

            // إضافة العنصر إلى الصفحة
            document.head.appendChild(styleElement);

            // إضافة مستمع لحدث afterprint للتنظيف
            var cleanupFunction = function() {
                try {
                    if (styleElement && styleElement.parentNode) {
                        document.head.removeChild(styleElement);
                    }
                    document.body.style.overflow = originalOverflow;
                    document.body.style.width = originalWidth;

                    // إظهار العناصر المخفية
                    if (elementsToHide) {
                        elementsToHide.forEach(function(element) {
                            element.style.display = '';
                        });
                    }

                    // إعادة تعيين المسافات
                    if (container) {
                        container.style.paddingTop = '';
                        container.style.marginTop = '';
                    }
                } catch (e) {
                    console.error("Error in cleanup:", e);
                }

                // إزالة المستمع
                window.removeEventListener('afterprint', cleanupFunction);
            };

            // إضافة مستمع لحدث afterprint
            window.addEventListener('afterprint', cleanupFunction);

            // طباعة الصفحة
            window.print();

            // تنظيف احتياطي بعد 3 ثوانٍ
            setTimeout(cleanupFunction, 3000);

        } catch (error) {
            console.error("Error printing:", error);
            alert("حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.");

            // تنظيف في حالة الخطأ
            try {
                if (styleElement && styleElement.parentNode) {
                    document.head.removeChild(styleElement);
                }
                if (originalOverflow !== undefined) {
                    document.body.style.overflow = originalOverflow;
                }
                if (originalWidth !== undefined) {
                    document.body.style.width = originalWidth;
                }
                if (elementsToHide) {
                    elementsToHide.forEach(function(element) {
                        element.style.display = '';
                    });
                }
                if (container) {
                    container.style.paddingTop = '';
                    container.style.marginTop = '';
                }
            } catch (e) {
                console.error("Error in error cleanup:", e);
            }
        }
    }

    // وظيفة تصدير إلى PDF حقيقي
    async function exportToPDF() {
        try {
            // إخفاء العناصر غير المطلوبة
            var noprint = document.querySelectorAll('.no-print');
            noprint.forEach(function(element) {
                element.style.display = 'none';
            });

            // الحصول على المحتوى المراد تحويله
            var element = document.querySelector('.container-fluid');

            // تطبيق تنسيق خاص للـ PDF قبل التقاط الصورة
            var pdfStyle = document.createElement('style');
            pdfStyle.id = 'pdf-export-style';
            pdfStyle.innerHTML = `
                .container-fluid {
                    transform: scale(1.0) !important;
                    transform-origin: top center !important;
                    width: 100% !important;
                    margin: 0 auto !important;
                    padding: 0 10px !important;
                }
                .table {
                    font-size: 11pt !important;
                    margin: 0 auto !important;
                    width: 98% !important;
                }
                .table th {
                    font-size: 10pt !important;
                    padding: 4px 2px !important;
                }
                .table td {
                    font-size: 10pt !important;
                    padding: 4px 2px !important;
                }
                .card-header h4 {
                    font-size: 16pt !important;
                    text-align: center !important;
                }
                .info-card {
                    font-size: 11pt !important;
                    margin: 5px auto !important;
                    width: 98% !important;
                }
                .row {
                    margin: 0 !important;
                    justify-content: center !important;
                }
            `;
            document.head.appendChild(pdfStyle);

            // انتظار قصير لتطبيق التنسيق
            await new Promise(resolve => setTimeout(resolve, 100));

            // تحويل إلى صورة مع إعدادات محسنة للوضع الأفقي
            const canvas = await html2canvas(element, {
                scale: 1.2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: element.scrollWidth,
                height: element.scrollHeight,
                scrollX: 0,
                scrollY: 0
            });

            // إنشاء PDF بالوضع الأفقي
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF('l', 'mm', 'a4'); // 'l' للوضع الأفقي (landscape)

            const imgData = canvas.toDataURL('image/png');
            const imgWidth = 295; // عرض A4 أفقي بالمليمتر
            const pageHeight = 210; // ارتفاع A4 أفقي بالمليمتر
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            let heightLeft = imgHeight;
            let position = 0;

            // إضافة الصفحة الأولى
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;

            // إضافة صفحات إضافية إذا لزم الأمر
            while (heightLeft >= 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
            }

            // حفظ الملف
            const fileName = "تقرير_الأدوية_" + new Date().toISOString().slice(0, 10) + ".pdf";
            pdf.save(fileName);

            // إزالة تنسيق PDF وإظهار العناصر المخفية
            var pdfStyleElement = document.getElementById('pdf-export-style');
            if (pdfStyleElement) {
                document.head.removeChild(pdfStyleElement);
            }

            noprint.forEach(function(element) {
                element.style.display = '';
            });

        } catch (error) {
            console.error("Error exporting to PDF:", error);
            alert("حدث خطأ أثناء التصدير إلى PDF. يرجى المحاولة مرة أخرى.");

            // تنظيف في حالة الخطأ
            var pdfStyleElement = document.getElementById('pdf-export-style');
            if (pdfStyleElement) {
                document.head.removeChild(pdfStyleElement);
            }

            // إظهار العناصر المخفية في حالة الخطأ
            var noprint = document.querySelectorAll('.no-print');
            noprint.forEach(function(element) {
                element.style.display = '';
            });
        }
    }

    // وظيفة تصدير البيانات إلى Excel مع تنسيق متقدم
    async function exportToExcel() {
        try {
            // إنشاء مصنف جديد
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('تقرير الأدوية');

            let currentRow = 1;

            // إضافة عنوان التقرير
            const reportTitle = document.querySelector('.card-header h4');
            const titleCell = worksheet.getCell('A' + currentRow);
            titleCell.value = reportTitle ? reportTitle.innerText : 'تقرير الأدوية';
            titleCell.font = { bold: true, size: 16, color: { argb: 'FFFFFFFF' } };
            titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF667EEA' } };
            titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.mergeCells('A' + currentRow + ':G' + currentRow);
            currentRow += 2;

            // إضافة معلومات التقرير
            const reportInfo = document.querySelectorAll('.info-card');
            reportInfo.forEach(function(info) {
                const infoCell = worksheet.getCell('A' + currentRow);
                infoCell.value = info.innerText;
                infoCell.font = { bold: true, size: 12 };
                infoCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FC' } };
                worksheet.mergeCells('A' + currentRow + ':G' + currentRow);
                currentRow++;
            });
            currentRow++; // سطر فارغ

            // الحصول على الجدول
            const table = document.querySelector('.table');
            if (table) {
                // عناوين الأعمدة
                const headers = [];
                table.querySelectorAll('thead th').forEach(function(th) {
                    headers.push(th.innerText);
                });

                headers.forEach((header, index) => {
                    const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                    cell.value = header;
                    cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF11998E' } };
                    cell.alignment = { horizontal: 'center', vertical: 'middle' };
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
                currentRow++;

                // بيانات الصفوف
                table.querySelectorAll('tbody tr').forEach(function(tr) {
                    const rowData = [];
                    tr.querySelectorAll('td').forEach(function(td) {
                        const text = td.innerText.trim();
                        if (!isNaN(text) && text !== '') {
                            rowData.push(parseFloat(text));
                        } else {
                            rowData.push(text);
                        }
                    });

                    rowData.forEach((data, index) => {
                        const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                        cell.value = data;
                        cell.alignment = { horizontal: 'center', vertical: 'middle' };
                        cell.border = {
                            top: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                            left: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                            bottom: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                            right: { style: 'thin', color: { argb: 'FFDDDDDD' } }
                        };
                    });
                    currentRow++;
                });

                // إجمالي التقرير
                const footerRow = table.querySelector('tfoot tr');
                if (footerRow) {
                    const totalData = [];
                    footerRow.querySelectorAll('th, td').forEach(function(cell) {
                        const text = cell.innerText.trim();
                        if (!isNaN(text) && text !== '') {
                            totalData.push(parseFloat(text));
                        } else {
                            totalData.push(text);
                        }
                    });

                    totalData.forEach((data, index) => {
                        const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                        cell.value = data;
                        cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF28A745' } };
                        cell.alignment = { horizontal: 'center', vertical: 'middle' };
                        cell.border = {
                            top: { style: 'thin' },
                            left: { style: 'thin' },
                            bottom: { style: 'thin' },
                            right: { style: 'thin' }
                        };
                    });
                }
            }

            // تحديد عرض الأعمدة
            worksheet.columns = [
                { width: 5 },   // #
                { width: 35 },  // اسم الدواء
                { width: 20 },  // العيادة
                { width: 18 },  // المنطقة
                { width: 15 },  // الكمية
                { width: 12 },  // السعر
                { width: 18 }   // التكلفة
            ];

            // تصدير الملف
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const fileName = "تقرير_الأدوية_منسق_" + new Date().toISOString().slice(0, 10) + ".xlsx";
            saveAs(blob, fileName);
        } catch (error) {
            console.error("Error exporting to Excel:", error);
            alert("حدث خطأ أثناء التصدير إلى Excel. يرجى المحاولة مرة أخرى.");
        }
    }
</script>
{% endblock %}
